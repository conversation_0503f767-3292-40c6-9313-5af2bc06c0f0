package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
	"pxpat-backend/pkg/cache"
)

// ContentCacheRepository 内容缓存仓库接口
type ContentCacheRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, contentCache *model.ContentCache) error
	GetByContentKSUID(ctx context.Context, contentKSUID string) (*model.ContentCache, error)
	Update(ctx context.Context, contentCache *model.ContentCache) error
	Delete(ctx context.Context, contentKSUID string) error
	BatchDelete(ctx context.Context, contentKSUIDs []string) error

	// 查询操作
	GetByContentType(ctx context.Context, contentType string, limit int) ([]*model.ContentCache, error)
	GetExpiredCaches(ctx context.Context, limit int) ([]*model.ContentCache, error)
	GetValidCaches(ctx context.Context, contentType string, limit int) ([]*model.ContentCache, error)

	// 缓存操作
	Set(ctx context.Context, contentKSUID, contentType, cacheData string, ttl time.Duration) error
	Get(ctx context.Context, contentKSUID string) (*model.ContentCache, error)
	Exists(ctx context.Context, contentKSUID string) (bool, error)
	Refresh(ctx context.Context, contentKSUID string, ttl time.Duration) error

	// 清理操作
	CleanupExpiredCaches(ctx context.Context) (int64, error)
	CleanupByContentType(ctx context.Context, contentType string) (int64, error)

	// 统计操作
	GetStats(ctx context.Context) (*model.ContentCacheStats, error)
	CountByContentType(ctx context.Context, contentType string) (int64, error)
	CountExpired(ctx context.Context) (int64, error)

	// 数据库实例获取（用于事务）
	GetDB() *gorm.DB
}

// contentCacheRepository 内容缓存仓库实现
type contentCacheRepository struct {
	db           *gorm.DB
	rdb          *redis.Client
	cacheManager cache.Manager
}

// NewContentCacheRepository 创建内容缓存仓库
func NewContentCacheRepository(db *gorm.DB, rdb *redis.Client, cacheManager cache.Manager) ContentCacheRepository {
	return &contentCacheRepository{
		db:           db,
		rdb:          rdb,
		cacheManager: cacheManager,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *contentCacheRepository) GetDB() *gorm.DB {
	return r.db
}

// Create 创建内容缓存
func (r *contentCacheRepository) Create(ctx context.Context, contentCache *model.ContentCache) error {
	log.Debug().
		Str("content_ksuid", contentCache.ContentKSUID).
		Str("content_type", contentCache.ContentType).
		Msg("开始创建内容缓存记录")

	err := r.db.WithContext(ctx).Create(contentCache).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentCache.ContentKSUID).
			Msg("创建内容缓存记录失败")
		return fmt.Errorf("failed to create content cache: %w", err)
	}

	log.Debug().
		Uint("cache_id", contentCache.ID).
		Str("content_ksuid", contentCache.ContentKSUID).
		Msg("创建内容缓存记录成功")

	return nil
}

// GetByContentKSUID 根据内容KSUID获取缓存
func (r *contentCacheRepository) GetByContentKSUID(ctx context.Context, contentKSUID string) (*model.ContentCache, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始获取内容缓存记录")

	var contentCache model.ContentCache
	err := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		First(&contentCache).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Debug().
				Str("content_ksuid", contentKSUID).
				Msg("内容缓存记录不存在")
			return nil, nil
		}
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容缓存记录失败")
		return nil, fmt.Errorf("failed to get content cache: %w", err)
	}

	// 检查是否过期
	if contentCache.IsExpired() {
		log.Debug().
			Str("content_ksuid", contentKSUID).
			Time("expires_at", contentCache.ExpiresAt).
			Msg("内容缓存已过期")
		return nil, nil
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("获取内容缓存记录成功")

	return &contentCache, nil
}

// Update 更新内容缓存
func (r *contentCacheRepository) Update(ctx context.Context, contentCache *model.ContentCache) error {
	log.Debug().
		Str("content_ksuid", contentCache.ContentKSUID).
		Msg("开始更新内容缓存记录")

	err := r.db.WithContext(ctx).Save(contentCache).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentCache.ContentKSUID).
			Msg("更新内容缓存记录失败")
		return fmt.Errorf("failed to update content cache: %w", err)
	}

	log.Debug().
		Str("content_ksuid", contentCache.ContentKSUID).
		Msg("更新内容缓存记录成功")

	return nil
}

// Delete 删除内容缓存
func (r *contentCacheRepository) Delete(ctx context.Context, contentKSUID string) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始删除内容缓存记录")

	result := r.db.WithContext(ctx).
		Where("content_ksuid = ?", contentKSUID).
		Delete(&model.ContentCache{})
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("删除内容缓存记录失败")
		return fmt.Errorf("failed to delete content cache: %w", result.Error)
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Int64("affected_rows", result.RowsAffected).
		Msg("删除内容缓存记录成功")

	return nil
}

// BatchDelete 批量删除内容缓存
func (r *contentCacheRepository) BatchDelete(ctx context.Context, contentKSUIDs []string) error {
	if len(contentKSUIDs) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(contentKSUIDs)).
		Msg("开始批量删除内容缓存记录")

	result := r.db.WithContext(ctx).
		Where("content_ksuid IN ?", contentKSUIDs).
		Delete(&model.ContentCache{})
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Int("count", len(contentKSUIDs)).
			Msg("批量删除内容缓存记录失败")
		return fmt.Errorf("failed to batch delete content caches: %w", result.Error)
	}

	log.Debug().
		Int64("deleted_count", result.RowsAffected).
		Int("requested_count", len(contentKSUIDs)).
		Msg("批量删除内容缓存记录成功")

	return nil
}

// GetByContentType 根据内容类型获取缓存
func (r *contentCacheRepository) GetByContentType(ctx context.Context, contentType string, limit int) ([]*model.ContentCache, error) {
	log.Debug().
		Str("content_type", contentType).
		Int("limit", limit).
		Msg("开始获取指定类型的内容缓存")

	if limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200
	}

	var caches []*model.ContentCache
	err := r.db.WithContext(ctx).
		Where("content_type = ? AND expires_at > ?", contentType, time.Now()).
		Order("created_at DESC").
		Limit(limit).
		Find(&caches).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", contentType).
			Msg("获取指定类型的内容缓存失败")
		return nil, fmt.Errorf("failed to get content caches by type: %w", err)
	}

	log.Debug().
		Str("content_type", contentType).
		Int("count", len(caches)).
		Msg("获取指定类型的内容缓存成功")

	return caches, nil
}

// GetExpiredCaches 获取过期的缓存
func (r *contentCacheRepository) GetExpiredCaches(ctx context.Context, limit int) ([]*model.ContentCache, error) {
	log.Debug().
		Int("limit", limit).
		Msg("开始获取过期的内容缓存")

	if limit <= 0 {
		limit = 100
	}
	if limit > 500 {
		limit = 500
	}

	var caches []*model.ContentCache
	err := r.db.WithContext(ctx).
		Where("expires_at <= ?", time.Now()).
		Order("expires_at ASC").
		Limit(limit).
		Find(&caches).Error
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取过期的内容缓存失败")
		return nil, fmt.Errorf("failed to get expired content caches: %w", err)
	}

	log.Debug().
		Int("count", len(caches)).
		Msg("获取过期的内容缓存成功")

	return caches, nil
}

// GetValidCaches 获取有效的缓存
func (r *contentCacheRepository) GetValidCaches(ctx context.Context, contentType string, limit int) ([]*model.ContentCache, error) {
	log.Debug().
		Str("content_type", contentType).
		Int("limit", limit).
		Msg("开始获取有效的内容缓存")

	if limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200
	}

	query := r.db.WithContext(ctx).Where("expires_at > ?", time.Now())
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	var caches []*model.ContentCache
	err := query.Order("updated_at DESC").
		Limit(limit).
		Find(&caches).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", contentType).
			Msg("获取有效的内容缓存失败")
		return nil, fmt.Errorf("failed to get valid content caches: %w", err)
	}

	log.Debug().
		Str("content_type", contentType).
		Int("count", len(caches)).
		Msg("获取有效的内容缓存成功")

	return caches, nil
}

// Set 设置内容缓存
func (r *contentCacheRepository) Set(ctx context.Context, contentKSUID, contentType, cacheData string, ttl time.Duration) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", contentType).
		Dur("ttl", ttl).
		Msg("开始设置内容缓存")

	// 检查是否已存在
	existingCache, err := r.GetByContentKSUID(ctx, contentKSUID)
	if err != nil {
		return err
	}

	if existingCache != nil {
		// 更新现有缓存
		existingCache.CacheData = cacheData
		existingCache.SetExpiration(ttl)
		return r.Update(ctx, existingCache)
	}

	// 创建新缓存
	newCache := model.NewContentCache(contentKSUID, contentType, cacheData, ttl)
	return r.Create(ctx, newCache)
}

// Get 获取内容缓存
func (r *contentCacheRepository) Get(ctx context.Context, contentKSUID string) (*model.ContentCache, error) {
	return r.GetByContentKSUID(ctx, contentKSUID)
}

// Exists 检查缓存是否存在
func (r *contentCacheRepository) Exists(ctx context.Context, contentKSUID string) (bool, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("开始检查内容缓存是否存在")

	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ContentCache{}).
		Where("content_ksuid = ? AND expires_at > ?", contentKSUID, time.Now()).
		Count(&count).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("检查内容缓存是否存在失败")
		return false, fmt.Errorf("failed to check content cache existence: %w", err)
	}

	exists := count > 0
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Bool("exists", exists).
		Msg("检查内容缓存是否存在完成")

	return exists, nil
}

// Refresh 刷新缓存过期时间
func (r *contentCacheRepository) Refresh(ctx context.Context, contentKSUID string, ttl time.Duration) error {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Dur("ttl", ttl).
		Msg("开始刷新内容缓存过期时间")

	newExpiresAt := time.Now().Add(ttl)
	result := r.db.WithContext(ctx).
		Model(&model.ContentCache{}).
		Where("content_ksuid = ?", contentKSUID).
		Updates(map[string]interface{}{
			"expires_at": newExpiresAt,
			"updated_at": time.Now(),
		})
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_ksuid", contentKSUID).
			Msg("刷新内容缓存过期时间失败")
		return fmt.Errorf("failed to refresh content cache: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		log.Debug().
			Str("content_ksuid", contentKSUID).
			Msg("内容缓存不存在，无法刷新")
		return nil
	}

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Time("new_expires_at", newExpiresAt).
		Msg("刷新内容缓存过期时间成功")

	return nil
}

// CleanupExpiredCaches 清理过期缓存
func (r *contentCacheRepository) CleanupExpiredCaches(ctx context.Context) (int64, error) {
	log.Info().Msg("开始清理过期的内容缓存")

	result := r.db.WithContext(ctx).
		Where("expires_at <= ?", time.Now()).
		Delete(&model.ContentCache{})
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Msg("清理过期的内容缓存失败")
		return 0, fmt.Errorf("failed to cleanup expired caches: %w", result.Error)
	}

	deletedCount := result.RowsAffected
	log.Info().
		Int64("deleted_count", deletedCount).
		Msg("清理过期的内容缓存完成")

	return deletedCount, nil
}

// CleanupByContentType 根据内容类型清理缓存
func (r *contentCacheRepository) CleanupByContentType(ctx context.Context, contentType string) (int64, error) {
	log.Info().
		Str("content_type", contentType).
		Msg("开始清理指定类型的内容缓存")

	result := r.db.WithContext(ctx).
		Where("content_type = ?", contentType).
		Delete(&model.ContentCache{})
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("content_type", contentType).
			Msg("清理指定类型的内容缓存失败")
		return 0, fmt.Errorf("failed to cleanup caches by content type: %w", result.Error)
	}

	deletedCount := result.RowsAffected
	log.Info().
		Str("content_type", contentType).
		Int64("deleted_count", deletedCount).
		Msg("清理指定类型的内容缓存完成")

	return deletedCount, nil
}

// GetStats 获取内容缓存统计
func (r *contentCacheRepository) GetStats(ctx context.Context) (*model.ContentCacheStats, error) {
	log.Debug().Msg("开始获取内容缓存统计")

	// 尝试从缓存获取
	cacheKey := "content_cache:stats:overall"
	var stats model.ContentCacheStats
	if err := r.cacheManager.Get(ctx, cacheKey, &stats); err == nil {
		log.Debug().Msg("从缓存获取内容缓存统计成功")
		return &stats, nil
	}

	// 获取总缓存数
	var totalCaches int64
	if err := r.db.WithContext(ctx).Model(&model.ContentCache{}).Count(&totalCaches).Error; err != nil {
		log.Error().Err(err).Msg("获取总缓存数失败")
		return nil, fmt.Errorf("failed to count total caches: %w", err)
	}

	// 获取按类型统计
	var typeStats []struct {
		ContentType string `json:"content_type"`
		Count       int64  `json:"count"`
	}
	err := r.db.WithContext(ctx).
		Model(&model.ContentCache{}).
		Select("content_type, COUNT(*) as count").
		Group("content_type").
		Find(&typeStats).Error
	if err != nil {
		log.Error().Err(err).Msg("获取按类型统计失败")
		return nil, fmt.Errorf("failed to get stats by type: %w", err)
	}

	cachesByType := make(map[string]int64)
	for _, stat := range typeStats {
		cachesByType[stat.ContentType] = stat.Count
	}

	// 获取过期缓存数
	var expiredCaches int64
	err = r.db.WithContext(ctx).
		Model(&model.ContentCache{}).
		Where("expires_at <= ?", time.Now()).
		Count(&expiredCaches).Error
	if err != nil {
		log.Error().Err(err).Msg("获取过期缓存数失败")
		return nil, fmt.Errorf("failed to count expired caches: %w", err)
	}

	// 计算平均缓存年龄
	var avgAgeHours float64
	err = r.db.WithContext(ctx).
		Model(&model.ContentCache{}).
		Select("AVG(EXTRACT(EPOCH FROM (NOW() - created_at))/3600) as avg_age").
		Where("expires_at > ?", time.Now()).
		Scan(&avgAgeHours).Error
	if err != nil {
		log.Error().Err(err).Msg("计算平均缓存年龄失败")
		avgAgeHours = 0
	}

	stats = model.ContentCacheStats{
		TotalCaches:   totalCaches,
		CachesByType:  cachesByType,
		ExpiredCaches: expiredCaches,
		CacheHitRate:  0, // 这个需要从应用层统计
		AvgCacheAge:   avgAgeHours,
	}

	// 缓存结果
	r.cacheManager.Set(ctx, cacheKey, &stats, 2*time.Minute)

	log.Debug().
		Int64("total_caches", totalCaches).
		Int64("expired_caches", expiredCaches).
		Msg("获取内容缓存统计成功")

	return &stats, nil
}

// CountByContentType 根据内容类型计数
func (r *contentCacheRepository) CountByContentType(ctx context.Context, contentType string) (int64, error) {
	log.Debug().
		Str("content_type", contentType).
		Msg("开始统计指定类型的缓存数量")

	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ContentCache{}).
		Where("content_type = ?", contentType).
		Count(&count).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("content_type", contentType).
			Msg("统计指定类型的缓存数量失败")
		return 0, fmt.Errorf("failed to count caches by content type: %w", err)
	}

	log.Debug().
		Str("content_type", contentType).
		Int64("count", count).
		Msg("统计指定类型的缓存数量成功")

	return count, nil
}

// CountExpired 统计过期缓存数量
func (r *contentCacheRepository) CountExpired(ctx context.Context) (int64, error) {
	log.Debug().Msg("开始统计过期缓存数量")

	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ContentCache{}).
		Where("expires_at <= ?", time.Now()).
		Count(&count).Error
	if err != nil {
		log.Error().
			Err(err).
			Msg("统计过期缓存数量失败")
		return 0, fmt.Errorf("failed to count expired caches: %w", err)
	}

	log.Debug().
		Int64("count", count).
		Msg("统计过期缓存数量成功")

	return count, nil
}
