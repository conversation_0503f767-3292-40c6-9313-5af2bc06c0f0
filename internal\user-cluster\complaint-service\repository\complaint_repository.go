package repository

import (
	"context"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
)

// ComplaintRepository 投诉数据访问层接口
type ComplaintRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, complaint *model.Complaint) error
	GetByKSUID(ctx context.Context, complaintKSUID string) (*model.Complaint, error)
	GetByID(ctx context.Context, id uint) (*model.Complaint, error)
	Update(ctx context.Context, complaint *model.Complaint) error
	Delete(ctx context.Context, complaintKSUID string) error

	// 查询操作
	GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.Complaint, int64, error)
	GetByContentKSUID(ctx context.Context, contentKSUID string, page, pageSize int) ([]*model.Complaint, int64, error)
	GetByStatus(ctx context.Context, status model.ComplaintStatus, page, pageSize int) ([]*model.Complaint, int64, error)
	GetByType(ctx context.Context, complaintType model.ComplaintType, page, pageSize int) ([]*model.Complaint, int64, error)
	
	// 复合查询
	GetWithFilters(ctx context.Context, filters ComplaintFilters) ([]*model.Complaint, int64, error)
	GetMyComplaints(ctx context.Context, userKSUID string, filters ComplaintFilters) ([]*model.Complaint, int64, error)
	GetComplaintsAgainstMe(ctx context.Context, userKSUID string, filters ComplaintFilters) ([]*model.Complaint, int64, error)

	// 统计操作
	CountByUserKSUID(ctx context.Context, userKSUID string) (int64, error)
	CountByStatus(ctx context.Context, status model.ComplaintStatus) (int64, error)
	CountByType(ctx context.Context, complaintType model.ComplaintType) (int64, error)
	GetStats(ctx context.Context, userKSUID string) (*ComplaintStats, error)
	CountTodayComplaintsByUser(ctx context.Context, userKSUID string) (int64, error)
	CountByComplainerKSUID(ctx context.Context, userKSUID string) (int64, error)
	CountByComplainerKSUIDAndStatus(ctx context.Context, userKSUID string, status model.ComplaintStatus) (int64, error)
	CountByAccusedKSUID(ctx context.Context, userKSUID string) (int64, error)
	CountByContentKSUID(ctx context.Context, contentKSUID string) (int64, error)
	CountByContentKSUIDAndStatus(ctx context.Context, contentKSUID string, status model.ComplaintStatus) (int64, error)
	GetByContentKSUIDAndStatus(ctx context.Context, contentKSUID string, status model.ComplaintStatus, page, pageSize int) ([]*model.Complaint, int64, error)

	// 业务操作
	UpdateStatus(ctx context.Context, complaintKSUID string, status model.ComplaintStatus, processorKSUID, processNote, resolution string) error
	ExistsByContentKSUID(ctx context.Context, contentKSUID, userKSUID string) (bool, error)
	GetPendingComplaints(ctx context.Context, page, pageSize int) ([]*model.Complaint, int64, error)

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// ComplaintEvidenceRepository 投诉证据文件数据访问层接口
type ComplaintEvidenceRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, evidence *model.ComplaintEvidence) error
	GetByID(ctx context.Context, id uint) (*model.ComplaintEvidence, error)
	GetByComplaintKSUID(ctx context.Context, complaintKSUID string) ([]*model.ComplaintEvidence, error)
	Update(ctx context.Context, evidence *model.ComplaintEvidence) error
	Delete(ctx context.Context, id uint) error
	DeleteByComplaintKSUID(ctx context.Context, complaintKSUID string) error

	// 批量操作
	BatchCreate(ctx context.Context, evidences []*model.ComplaintEvidence) error
	BatchDelete(ctx context.Context, ids []uint) error

	// 查询操作
	CountByComplaintKSUID(ctx context.Context, complaintKSUID string) (int64, error)
	GetTotalSizeByComplaintKSUID(ctx context.Context, complaintKSUID string) (int64, error)

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// ViolationCategoryRepository 违规类别数据访问层接口
type ViolationCategoryRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, category *model.ViolationCategory) error
	GetByCode(ctx context.Context, code string) (*model.ViolationCategory, error)
	GetByID(ctx context.Context, id uint) (*model.ViolationCategory, error)
	Update(ctx context.Context, category *model.ViolationCategory) error
	Delete(ctx context.Context, code string) error

	// 查询操作
	GetAll(ctx context.Context) ([]*model.ViolationCategory, error)
	GetByParentCode(ctx context.Context, parentCode string) ([]*model.ViolationCategory, error)
	GetRootCategories(ctx context.Context) ([]*model.ViolationCategory, error)
	GetActiveCategories(ctx context.Context) ([]*model.ViolationCategory, error)
	GetCategoryTree(ctx context.Context) ([]*model.ViolationCategory, error)

	// 业务操作
	ExistsByCode(ctx context.Context, code string) (bool, error)
	UpdateStatus(ctx context.Context, code string, isActive bool) error
	GetMaxSortOrder(ctx context.Context, parentCode string) (int, error)

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// ComplaintFilters 投诉查询过滤条件
type ComplaintFilters struct {
	Page         int                   // 页码
	PageSize     int                   // 每页数量
	Type         model.ComplaintType   // 投诉类型
	Status       model.ComplaintStatus // 状态
	ContentType  string                // 内容类型
	StartDate    string                // 开始日期
	EndDate      string                // 结束日期
	SortBy       string                // 排序字段
	SortOrder    string                // 排序方向
	UserKSUID    string                // 用户KSUID
	ProcessorKSUID string              // 处理人KSUID
}

// ComplaintStats 投诉统计信息
type ComplaintStats struct {
	TotalComplaints     int64 // 总投诉数
	PendingComplaints   int64 // 待处理投诉数
	CompletedComplaints int64 // 已完成投诉数
	CancelledComplaints int64 // 已取消投诉数
	MyComplaints        int64 // 我的投诉数
	ComplaintsAgainstMe int64 // 针对我的投诉数
	TodayComplaints     int64 // 今日投诉数
}

// 错误定义
var (
	ErrComplaintNotFound         = NewRepositoryError("complaint not found")
	ErrComplaintAlreadyExists    = NewRepositoryError("complaint already exists")
	ErrComplaintEvidenceNotFound = NewRepositoryError("complaint evidence not found")
	ErrViolationCategoryNotFound = NewRepositoryError("violation category not found")
	ErrViolationCategoryExists   = NewRepositoryError("violation category already exists")
	ErrInvalidComplaintStatus    = NewRepositoryError("invalid complaint status")
	ErrInvalidComplaintType      = NewRepositoryError("invalid complaint type")
	ErrComplaintLimitExceeded    = NewRepositoryError("complaint limit exceeded")
	ErrEvidenceFileLimitExceeded = NewRepositoryError("evidence file limit exceeded")
)

// RepositoryError 仓库错误类型
type RepositoryError struct {
	Message string
}

func (e *RepositoryError) Error() string {
	return e.Message
}

func NewRepositoryError(message string) *RepositoryError {
	return &RepositoryError{Message: message}
}
