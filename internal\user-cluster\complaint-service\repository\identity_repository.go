package repository

import (
	"context"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
)

// IdentityVerificationRepository 身份认证数据访问层接口
type IdentityVerificationRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, verification *model.IdentityVerification) error
	GetByKSUID(ctx context.Context, verificationKSUID string) (*model.IdentityVerification, error)
	GetByID(ctx context.Context, id uint) (*model.IdentityVerification, error)
	Update(ctx context.Context, verification *model.IdentityVerification) error
	Delete(ctx context.Context, verificationKSUID string) error

	// 查询操作
	GetByUserKSUID(ctx context.Context, userKSUID string) (*model.IdentityVerification, error)
	GetByStatus(ctx context.Context, status model.IdentityStatus, page, pageSize int) ([]*model.IdentityVerification, int64, error)
	GetByType(ctx context.Context, identityType model.IdentityType, page, pageSize int) ([]*model.IdentityVerification, int64, error)
	
	// 复合查询
	GetWithFilters(ctx context.Context, filters IdentityFilters) ([]*model.IdentityVerification, int64, error)

	// 统计操作
	CountByUserKSUID(ctx context.Context, userKSUID string) (int64, error)
	CountByStatus(ctx context.Context, status model.IdentityStatus) (int64, error)
	CountByType(ctx context.Context, identityType model.IdentityType) (int64, error)

	// 业务操作
	UpdateStatus(ctx context.Context, verificationKSUID string, status model.IdentityStatus, reviewerKSUID, reviewNote, rejectReason string) error
	ExistsByUserKSUID(ctx context.Context, userKSUID string) (bool, error)
	GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.IdentityVerification, int64, error)
	GetExpiredVerifications(ctx context.Context) ([]*model.IdentityVerification, error)
	IsUserVerified(ctx context.Context, userKSUID string) (bool, error)

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// CountryRepository 国家地区数据访问层接口
type CountryRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, country *model.Country) error
	GetByCode(ctx context.Context, code string) (*model.Country, error)
	GetByID(ctx context.Context, id uint) (*model.Country, error)
	Update(ctx context.Context, country *model.Country) error
	Delete(ctx context.Context, code string) error

	// 查询操作
	GetAll(ctx context.Context) ([]*model.Country, error)
	GetByContinent(ctx context.Context, continent string) ([]*model.Country, error)
	GetByRegion(ctx context.Context, region string) ([]*model.Country, error)
	GetActive(ctx context.Context) ([]*model.Country, error)
	Search(ctx context.Context, keyword string) ([]*model.Country, error)

	// 业务操作
	ExistsByCode(ctx context.Context, code string) (bool, error)
	UpdateStatus(ctx context.Context, code string, isActive bool) error
	GetMaxSortOrder(ctx context.Context) (int, error)
	GetChinaRegions(ctx context.Context) ([]*model.Country, error) // 获取中国特别行政区

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// TrademarkCategoryRepository 商标类型数据访问层接口
type TrademarkCategoryRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, category *model.TrademarkCategory) error
	GetByNumber(ctx context.Context, categoryNumber int) (*model.TrademarkCategory, error)
	GetByID(ctx context.Context, id uint) (*model.TrademarkCategory, error)
	Update(ctx context.Context, category *model.TrademarkCategory) error
	Delete(ctx context.Context, categoryNumber int) error

	// 查询操作
	GetAll(ctx context.Context) ([]*model.TrademarkCategory, error)
	GetActive(ctx context.Context) ([]*model.TrademarkCategory, error)
	Search(ctx context.Context, keyword string) ([]*model.TrademarkCategory, error)

	// 业务操作
	ExistsByNumber(ctx context.Context, categoryNumber int) (bool, error)
	UpdateStatus(ctx context.Context, categoryNumber int, isActive bool) error
	GetMaxSortOrder(ctx context.Context) (int, error)

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// IdentityFilters 身份认证查询过滤条件
type IdentityFilters struct {
	Page         int                  // 页码
	PageSize     int                  // 每页数量
	Type         model.IdentityType   // 身份类型
	Status       model.IdentityStatus // 状态
	StartDate    string               // 开始日期
	EndDate      string               // 结束日期
	SortBy       string               // 排序字段
	SortOrder    string               // 排序方向
	UserKSUID    string               // 用户KSUID
	ReviewerKSUID string              // 审核人KSUID
}

// 错误定义
var (
	ErrIdentityVerificationNotFound = NewRepositoryError("identity verification not found")
	ErrIdentityVerificationExists   = NewRepositoryError("identity verification already exists")
	ErrCountryNotFound              = NewRepositoryError("country not found")
	ErrCountryExists                = NewRepositoryError("country already exists")
	ErrTrademarkCategoryNotFound    = NewRepositoryError("trademark category not found")
	ErrTrademarkCategoryExists      = NewRepositoryError("trademark category already exists")
	ErrInvalidIdentityStatus        = NewRepositoryError("invalid identity status")
	ErrInvalidIdentityType          = NewRepositoryError("invalid identity type")
	ErrUserAlreadyVerified          = NewRepositoryError("user already verified")
)
