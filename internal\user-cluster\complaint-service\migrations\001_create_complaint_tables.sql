-- 投诉服务数据库迁移文件
-- 创建投诉相关表结构

-- 1. 投诉记录表
CREATE TABLE IF NOT EXISTS complaint_complaints (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    -- 基础字段
    complaint_ksuid VARCHAR(27) NOT NULL UNIQUE COMMENT '投诉唯一标识',
    user_ksuid VARCHAR(27) NOT NULL COMMENT '投诉人用户KSUID',
    content_ksuid VARCHAR(27) NOT NULL COMMENT '被投诉内容的唯一KSUID',
    content_type VARCHAR(50) NOT NULL COMMENT '被投诉内容的类型',
    type ENUM('piracy', 'content', 'rights') NOT NULL COMMENT '投诉类型',
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '投诉状态',
    
    -- 投诉详情
    title VARCHAR(255) NOT NULL COMMENT '投诉标题',
    description TEXT COMMENT '详细描述',
    user_role ENUM('original_creator', 'passerby') COMMENT '用户角色(仅盗版投诉使用)',
    
    -- 盗版投诉特有字段
    original_url VARCHAR(1024) COMMENT '原视频出处',
    work_url VARCHAR(1024) COMMENT '本站作品地址',
    
    -- 稿件投诉特有字段
    violation_type VARCHAR(100) COMMENT '违规类型',
    violation_sub_type VARCHAR(100) COMMENT '违规子类型',
    
    -- 处理信息
    processor_ksuid VARCHAR(27) COMMENT '处理人KSUID',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    process_note TEXT COMMENT '处理备注',
    resolution TEXT COMMENT '处理结果',
    
    INDEX idx_complaint_ksuid (complaint_ksuid),
    INDEX idx_user_ksuid (user_ksuid),
    INDEX idx_content_ksuid (content_ksuid),
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='投诉记录表';

-- 2. 投诉证据文件表
CREATE TABLE IF NOT EXISTS complaint_evidences (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    complaint_ksuid VARCHAR(27) NOT NULL COMMENT '关联的投诉KSUID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_url VARCHAR(1024) NOT NULL COMMENT '文件URL',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小',
    content_type VARCHAR(100) COMMENT '文件类型',
    bucket_name VARCHAR(100) COMMENT '存储桶名称',
    object_name VARCHAR(512) COMMENT '对象名称',
    
    INDEX idx_complaint_ksuid (complaint_ksuid),
    INDEX idx_deleted_at (deleted_at),
    FOREIGN KEY (complaint_ksuid) REFERENCES complaint_complaints(complaint_ksuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='投诉证据文件表';

-- 3. 违规类别表
CREATE TABLE IF NOT EXISTS complaint_violation_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '类别代码',
    name VARCHAR(100) NOT NULL COMMENT '类别名称',
    description TEXT COMMENT '类别描述',
    parent_code VARCHAR(50) COMMENT '父类别代码',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    INDEX idx_code (code),
    INDEX idx_parent_code (parent_code),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='违规类别表';

-- 4. 身份认证表
CREATE TABLE IF NOT EXISTS complaint_identity_verifications (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    -- 基础字段
    verification_ksuid VARCHAR(27) NOT NULL UNIQUE COMMENT '认证唯一标识',
    user_ksuid VARCHAR(27) NOT NULL COMMENT '用户KSUID',
    type ENUM('natural', 'legal_entity') NOT NULL COMMENT '身份类型',
    status ENUM('pending', 'approved', 'rejected', 'expired') DEFAULT 'pending' COMMENT '认证状态',
    
    -- 自然人信息
    real_name VARCHAR(100) COMMENT '真实姓名',
    phone_number VARCHAR(20) COMMENT '手机号',
    email VARCHAR(255) COMMENT '邮箱',
    id_card VARCHAR(32) COMMENT '身份证号',
    address VARCHAR(500) COMMENT '联系地址',
    postal_code VARCHAR(20) COMMENT '邮政编码',
    fax_number VARCHAR(20) COMMENT '传真号',
    landline_number VARCHAR(20) COMMENT '座机号',
    
    -- 法人/非法人组织信息
    organization_name VARCHAR(255) COMMENT '企业/机构/单位/团体全称',
    certificate_number VARCHAR(100) COMMENT '证件编号(营业执照证件编号)',
    certificate_start_at TIMESTAMP NULL COMMENT '证件有效期开始时间',
    certificate_end_at TIMESTAMP NULL COMMENT '证件有效期结束时间',
    contact_name VARCHAR(100) COMMENT '联系人姓名',
    contact_id_card VARCHAR(32) COMMENT '联系人身份证号',
    contact_email VARCHAR(255) COMMENT '联系邮箱',
    contact_phone VARCHAR(20) COMMENT '联系人手机号',
    contact_address VARCHAR(500) COMMENT '联系地址',
    contact_postal_code VARCHAR(20) COMMENT '邮政编码',
    contact_fax VARCHAR(20) COMMENT '传真号',
    contact_landline VARCHAR(20) COMMENT '座机号',
    
    -- 审核信息
    reviewer_ksuid VARCHAR(27) COMMENT '审核人KSUID',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_note TEXT COMMENT '审核备注',
    reject_reason TEXT COMMENT '拒绝原因',
    
    INDEX idx_verification_ksuid (verification_ksuid),
    INDEX idx_user_ksuid (user_ksuid),
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='身份认证表';

-- 5. 国家地区表
CREATE TABLE IF NOT EXISTS complaint_countries (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    code VARCHAR(10) NOT NULL UNIQUE COMMENT '国家代码(如CN, US, HK, MO, TW)',
    name VARCHAR(100) NOT NULL COMMENT '国家名称',
    name_en VARCHAR(100) COMMENT '英文名称',
    name_local VARCHAR(100) COMMENT '本地名称',
    continent VARCHAR(50) COMMENT '所属大洲',
    region VARCHAR(100) COMMENT '地区',
    sub_region VARCHAR(100) COMMENT '子地区',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    flag VARCHAR(10) COMMENT '国旗emoji',
    phone_code VARCHAR(10) COMMENT '电话区号',
    currency VARCHAR(10) COMMENT '货币代码',
    time_zone VARCHAR(50) COMMENT '时区',
    
    INDEX idx_code (code),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国家地区表';

-- 6. 商标类型表
CREATE TABLE IF NOT EXISTS complaint_trademark_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    category_number INT NOT NULL UNIQUE COMMENT '类别号(如1, 2, 3...)',
    name VARCHAR(200) NOT NULL COMMENT '类别名称(如"第1类.化学原料")',
    description TEXT COMMENT '类别描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    
    INDEX idx_category_number (category_number),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商标类型表';
