投诉管理界面(必须登录):
    我发起的申诉:
        任务状态有进行中,已完成两种
    被投诉的内容:
        任务状态有进行中,已完成两种
    存储申诉信息的表必须有如下字段:
        content_ksuid // 存储申诉内容的唯一ksuid,通过此ksuid获取具体内容
        content_type // 存储申诉内容的类型,用来向指定服务请求展示信息

身份认证功能(必须登录):
    Step1 资质认证:
        自然人:
            前端填写信息: 
                真实姓名,
                手机号,
                验证码
                邮箱,
                身份证,
                联系地址(选填),
                邮政编码(选填),
                传真号(选填),
                座机号(选填)

        法人/非法人组织:
            前端填写信息:
                名称(企业/机构/单位/团体全称),
                证件编号(营业执照证件编号),
                证件有效期:
                    开始时间
                    结束时间
                联系人姓名,
                联系人身份证号,
                联系邮箱,
                联系人手机号,
                联系地址(选填),
                邮政编码(选填),
                传真号(选填),
                座机号(选填)
    Step2 权益认证:
        是否代理:
            代理人
            权利人
        权利人/代理人姓名,
        授权期限: 
            起始时间
            结束时间
        授权证明: 此功能需要上传
        权益类型:
            著作权:
                著作类型(可能会传递多个):
                    视频
                    动漫
                    漫画
                    小说
                著作名称,
                地区: 要有一个表,存储全球所有国家,其中中国香港 中国澳门 中国台湾单独列出来
                期限:
                    起始时间,
                    结束时间,
                权利人证明文件: 此功能需用户上传图片
            商标权(可能会传递多个):
                商标类型: 
                    第1类.化学原料
                    第2类.颜料油漆
                    第3类.日化用品
                    ...(也需要有一个表,存储这种xx类,需要获取国家官网上列出来的品类)
                商标名称
                地区: 参考著作权中的地区
                期限:
                    起始时间,
                    结束时间,
                证明: 此功能需用户上传图片
            商誉权:
                证明: 此功能需用户上传图片
            人格权:
                权益类型:
                    姓名权
                    肖像权
                    名誉权
                    隐私权
                    其他
                证明: 此功能需用户上传图片
            昵称权:
                证明: 此功能需用户上传图片
    Step3 投诉申请
        创建一个接口,用户获取前端传递的前端获取到的所有数据
        

盗版稿件投诉: 
    你的角色:
        原创者
        路人
    本站作品地址,
    原视频出处,
    作品描述(可选),
    上传证据(可选): 此功能需用户上传
    拉黑 指定用户: 通过调用用户服务中,黑名单路由的拉黑接口

稿件投诉:
    选项:
        违反法律法规:
            违法违禁
            赌博诈骗
            盗搬我的稿件
            违法信息外链
            侵权申诉
        谣言及不实信息:
            涉政谣言
            涉社会事件谣言
            虚假不实信息
        投稿不规范:
            违规推广
            转载/自制错误
            其他不规范行为
        不友善行为:
            人身攻击
            引战
        违反公序良俗:
            色情低俗
            危险行为
            观感不适
            血腥暴力
            青少年不良信息
            其他
    详细描述,
    提交图片: 此功能需要用户上传图片

    上面的选项中,除了[盗搬我的稿件],[侵权申诉]剩下选项全部都需要接收详细描述和提交图片传递的信息

在存储服务下新增投诉存储,用于接收有关投诉上传的图片.可以参考用户存储中的实现.