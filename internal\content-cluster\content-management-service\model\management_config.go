package model

import (
	"time"
	"gorm.io/gorm"
)

// ManagementConfig 管理配置模型
type ManagementConfig struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	ConfigKey   string    `gorm:"type:varchar(100);not null;uniqueIndex" json:"config_key"`
	ConfigValue string    `gorm:"type:text;not null" json:"config_value"`
	ConfigType  string    `gorm:"type:varchar(20);not null" json:"config_type"` // string, int, bool, json
	Description string    `gorm:"type:text" json:"description"`
	Category    string    `gorm:"type:varchar(50);not null;index" json:"category"`
	IsActive    bool      `gorm:"default:true" json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (ManagementConfig) TableName() string {
	return "management_configs"
}

// BeforeCreate GORM钩子，创建前执行
func (mc *ManagementConfig) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if mc.CreatedAt.IsZero() {
		mc.CreatedAt = now
	}
	if mc.UpdatedAt.IsZero() {
		mc.UpdatedAt = now
	}
	return nil
}

// BeforeUpdate GORM钩子，更新前执行
func (mc *ManagementConfig) BeforeUpdate(tx *gorm.DB) error {
	mc.UpdatedAt = time.Now()
	return nil
}

// ConfigCategory 配置分类常量
const (
	ConfigCategoryLimits     = "limits"      // 限制配置
	ConfigCacheCache        = "cache"       // 缓存配置
	ConfigCategorySecurity  = "security"    // 安全配置
	ConfigCategoryService   = "service"     // 服务配置
	ConfigCategoryFeature   = "feature"     // 功能配置
	ConfigCategorySystem    = "system"      // 系统配置
)

// ConfigType 配置类型常量
const (
	ConfigTypeString = "string"
	ConfigTypeInt    = "int"
	ConfigTypeBool   = "bool"
	ConfigTypeJSON   = "json"
	ConfigTypeFloat  = "float"
)

// 预定义配置键常量
const (
	// 限制配置
	ConfigKeyMaxBatchSize     = "max_batch_size"
	ConfigKeyMaxSearchResults = "max_search_results"
	ConfigKeyMaxPageSize      = "max_page_size"
	ConfigKeyDefaultPageSize  = "default_page_size"
	
	// 缓存配置
	ConfigKeyContentTTL     = "content_ttl"
	ConfigKeyInteractionTTL = "interaction_ttl"
	ConfigKeyUserContentTTL = "user_content_ttl"
	ConfigKeyStatsTTL       = "stats_ttl"
	
	// 安全配置
	ConfigKeyRateLimitEnabled = "rate_limit_enabled"
	ConfigKeyRateLimitRPM     = "rate_limit_rpm"
	ConfigKeyAuthRequired     = "auth_required"
	
	// 功能配置
	ConfigKeyEnableVideoService       = "enable_video_service"
	ConfigKeyEnableNovelService       = "enable_novel_service"
	ConfigKeyEnableMusicService       = "enable_music_service"
	ConfigKeyEnableInteractionService = "enable_interaction_service"
	
	// 系统配置
	ConfigKeyMaintenanceMode = "maintenance_mode"
	ConfigKeyDebugMode       = "debug_mode"
	ConfigKeyLogLevel        = "log_level"
)

// NewManagementConfig 创建新的管理配置
func NewManagementConfig(key, value, configType, description, category string) *ManagementConfig {
	now := time.Now()
	return &ManagementConfig{
		ConfigKey:   key,
		ConfigValue: value,
		ConfigType:  configType,
		Description: description,
		Category:    category,
		IsActive:    true,
		CreatedAt:   now,
		UpdatedAt:   now,
	}
}

// IsValidConfigType 检查配置类型是否有效
func IsValidConfigType(configType string) bool {
	validTypes := []string{
		ConfigTypeString,
		ConfigTypeInt,
		ConfigTypeBool,
		ConfigTypeJSON,
		ConfigTypeFloat,
	}
	
	for _, validType := range validTypes {
		if configType == validType {
			return true
		}
	}
	return false
}

// IsValidCategory 检查配置分类是否有效
func IsValidCategory(category string) bool {
	validCategories := []string{
		ConfigCategoryLimits,
		ConfigCacheCache,
		ConfigCategorySecurity,
		ConfigCategoryService,
		ConfigCategoryFeature,
		ConfigCategorySystem,
	}
	
	for _, validCategory := range validCategories {
		if category == validCategory {
			return true
		}
	}
	return false
}

// ConfigList 配置列表
type ConfigList struct {
	Configs    []*ManagementConfig `json:"configs"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	Limit      int                 `json:"limit"`
	TotalPages int                 `json:"total_pages"`
}

// ConfigQuery 配置查询条件
type ConfigQuery struct {
	Category string `json:"category,omitempty"`
	IsActive *bool  `json:"is_active,omitempty"`
	Page     int    `json:"page,omitempty"`
	Limit    int    `json:"limit,omitempty"`
}

// ConfigUpdateRequest 配置更新请求
type ConfigUpdateRequest struct {
	ConfigValue string `json:"config_value" binding:"required"`
	Description string `json:"description,omitempty"`
	IsActive    *bool  `json:"is_active,omitempty"`
}

// ConfigStats 配置统计
type ConfigStats struct {
	TotalConfigs       int64            `json:"total_configs"`
	ActiveConfigs      int64            `json:"active_configs"`
	InactiveConfigs    int64            `json:"inactive_configs"`
	ConfigsByCategory  map[string]int64 `json:"configs_by_category"`
	ConfigsByType      map[string]int64 `json:"configs_by_type"`
	LastUpdated        *time.Time       `json:"last_updated,omitempty"`
}

// DefaultConfigs 默认配置列表
var DefaultConfigs = []*ManagementConfig{
	// 限制配置
	NewManagementConfig(ConfigKeyMaxBatchSize, "100", ConfigTypeInt, "批量操作最大数量", ConfigCategoryLimits),
	NewManagementConfig(ConfigKeyMaxSearchResults, "1000", ConfigTypeInt, "搜索结果最大数量", ConfigCategoryLimits),
	NewManagementConfig(ConfigKeyMaxPageSize, "100", ConfigTypeInt, "分页最大大小", ConfigCategoryLimits),
	NewManagementConfig(ConfigKeyDefaultPageSize, "20", ConfigTypeInt, "默认分页大小", ConfigCategoryLimits),
	
	// 缓存配置
	NewManagementConfig(ConfigKeyContentTTL, "300", ConfigTypeInt, "内容缓存TTL（秒）", ConfigCacheCache),
	NewManagementConfig(ConfigKeyInteractionTTL, "60", ConfigTypeInt, "交互统计缓存TTL（秒）", ConfigCacheCache),
	NewManagementConfig(ConfigKeyUserContentTTL, "180", ConfigTypeInt, "用户内容缓存TTL（秒）", ConfigCacheCache),
	NewManagementConfig(ConfigKeyStatsTTL, "600", ConfigTypeInt, "统计数据缓存TTL（秒）", ConfigCacheCache),
	
	// 安全配置
	NewManagementConfig(ConfigKeyRateLimitEnabled, "true", ConfigTypeBool, "是否启用限流", ConfigCategorySecurity),
	NewManagementConfig(ConfigKeyRateLimitRPM, "1000", ConfigTypeInt, "每分钟请求数限制", ConfigCategorySecurity),
	NewManagementConfig(ConfigKeyAuthRequired, "true", ConfigTypeBool, "是否需要认证", ConfigCategorySecurity),
	
	// 功能配置
	NewManagementConfig(ConfigKeyEnableVideoService, "true", ConfigTypeBool, "是否启用视频服务", ConfigCategoryFeature),
	NewManagementConfig(ConfigKeyEnableNovelService, "false", ConfigTypeBool, "是否启用小说服务", ConfigCategoryFeature),
	NewManagementConfig(ConfigKeyEnableMusicService, "false", ConfigTypeBool, "是否启用音乐服务", ConfigCategoryFeature),
	NewManagementConfig(ConfigKeyEnableInteractionService, "true", ConfigTypeBool, "是否启用交互服务", ConfigCategoryFeature),
	
	// 系统配置
	NewManagementConfig(ConfigKeyMaintenanceMode, "false", ConfigTypeBool, "维护模式", ConfigCategorySystem),
	NewManagementConfig(ConfigKeyDebugMode, "false", ConfigTypeBool, "调试模式", ConfigCategorySystem),
	NewManagementConfig(ConfigKeyLogLevel, "info", ConfigTypeString, "日志级别", ConfigCategorySystem),
}

// GetDefaultConfigByKey 根据键获取默认配置
func GetDefaultConfigByKey(key string) *ManagementConfig {
	for _, config := range DefaultConfigs {
		if config.ConfigKey == key {
			return config
		}
	}
	return nil
}

// GetDefaultConfigsByCategory 根据分类获取默认配置
func GetDefaultConfigsByCategory(category string) []*ManagementConfig {
	var configs []*ManagementConfig
	for _, config := range DefaultConfigs {
		if config.Category == category {
			configs = append(configs, config)
		}
	}
	return configs
}

// ConfigChangeLog 配置变更日志
type ConfigChangeLog struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	ConfigKey   string    `gorm:"type:varchar(100);not null;index" json:"config_key"`
	OldValue    string    `gorm:"type:text" json:"old_value"`
	NewValue    string    `gorm:"type:text" json:"new_value"`
	ChangedBy   string    `gorm:"type:varchar(32);not null" json:"changed_by"`
	ChangeReason string   `gorm:"type:text" json:"change_reason"`
	IPAddress   string    `gorm:"type:varchar(45)" json:"ip_address"`
	CreatedAt   time.Time `json:"created_at"`
}

// TableName 指定表名
func (ConfigChangeLog) TableName() string {
	return "config_change_logs"
}

// NewConfigChangeLog 创建配置变更日志
func NewConfigChangeLog(configKey, oldValue, newValue, changedBy, reason, ipAddress string) *ConfigChangeLog {
	return &ConfigChangeLog{
		ConfigKey:    configKey,
		OldValue:     oldValue,
		NewValue:     newValue,
		ChangedBy:    changedBy,
		ChangeReason: reason,
		IPAddress:    ipAddress,
		CreatedAt:    time.Now(),
	}
}
