package publisher

import (
	"context"
	"encoding/json"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/streadway/amqp"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
)

// ComplaintPublisher 投诉消息发布者接口
type ComplaintPublisher interface {
	// 发布投诉创建事件
	PublishComplaintCreated(ctx context.Context, complaint *model.Complaint) error
	
	// 发布投诉处理事件
	PublishComplaintProcessed(ctx context.Context, complaint *model.Complaint) error
	
	// 发布身份认证事件
	PublishIdentityVerificationUpdated(ctx context.Context, verification *model.IdentityVerification) error
	
	// 发布权益认证事件
	PublishRightsVerificationUpdated(ctx context.Context, verification *model.RightsVerification) error
}

// complaintPublisher 投诉消息发布者实现
type complaintPublisher struct {
	conn    *amqp.Connection
	channel *amqp.Channel
	exchange string
}

// NewComplaintPublisher 创建投诉消息发布者实例
func NewComplaintPublisher(conn *amqp.Connection, exchange string) (ComplaintPublisher, error) {
	channel, err := conn.Channel()
	if err != nil {
		return nil, err
	}

	// 声明交换机
	err = channel.ExchangeDeclare(
		exchange,
		"topic",
		true,  // durable
		false, // auto-deleted
		false, // internal
		false, // no-wait
		nil,   // arguments
	)
	if err != nil {
		return nil, err
	}

	return &complaintPublisher{
		conn:     conn,
		channel:  channel,
		exchange: exchange,
	}, nil
}

// ComplaintCreatedEvent 投诉创建事件
type ComplaintCreatedEvent struct {
	ComplaintKSUID string                `json:"complaint_ksuid"`
	ComplainerKSUID string               `json:"complainer_ksuid"`
	AccusedKSUID   string                `json:"accused_ksuid"`
	ContentKSUID   string                `json:"content_ksuid"`
	Type           model.ComplaintType   `json:"type"`
	Status         model.ComplaintStatus `json:"status"`
	Title          string                `json:"title"`
	Description    string                `json:"description"`
	CreatedAt      time.Time             `json:"created_at"`
	EventType      string                `json:"event_type"`
	Timestamp      time.Time             `json:"timestamp"`
}

// ComplaintProcessedEvent 投诉处理事件
type ComplaintProcessedEvent struct {
	ComplaintKSUID string                `json:"complaint_ksuid"`
	ComplainerKSUID string               `json:"complainer_ksuid"`
	AccusedKSUID   string                `json:"accused_ksuid"`
	ContentKSUID   string                `json:"content_ksuid"`
	Type           model.ComplaintType   `json:"type"`
	Status         model.ComplaintStatus `json:"status"`
	ProcessNote    string                `json:"process_note"`
	ProcessorKSUID string                `json:"processor_ksuid"`
	ProcessedAt    time.Time             `json:"processed_at"`
	EventType      string                `json:"event_type"`
	Timestamp      time.Time             `json:"timestamp"`
}

// IdentityVerificationUpdatedEvent 身份认证更新事件
type IdentityVerificationUpdatedEvent struct {
	VerificationKSUID string                        `json:"verification_ksuid"`
	UserKSUID         string                        `json:"user_ksuid"`
	Type              model.IdentityType            `json:"type"`
	Status            model.IdentityStatus          `json:"status"`
	ReviewerKSUID     string                        `json:"reviewer_ksuid"`
	ReviewNote        string                        `json:"review_note"`
	ReviewedAt        time.Time                     `json:"reviewed_at"`
	EventType         string                        `json:"event_type"`
	Timestamp         time.Time                     `json:"timestamp"`
}

// RightsVerificationUpdatedEvent 权益认证更新事件
type RightsVerificationUpdatedEvent struct {
	RightsKSUID    string              `json:"rights_ksuid"`
	UserKSUID      string              `json:"user_ksuid"`
	Type           model.RightsType    `json:"type"`
	Status         model.RightsStatus  `json:"status"`
	ReviewerKSUID  string              `json:"reviewer_ksuid"`
	ReviewNote     string              `json:"review_note"`
	ReviewedAt     time.Time           `json:"reviewed_at"`
	EventType      string              `json:"event_type"`
	Timestamp      time.Time           `json:"timestamp"`
}

// PublishComplaintCreated 发布投诉创建事件
func (p *complaintPublisher) PublishComplaintCreated(ctx context.Context, complaint *model.Complaint) error {
	event := ComplaintCreatedEvent{
		ComplaintKSUID:  complaint.ComplaintKSUID,
		ComplainerKSUID: complaint.ComplainerKSUID,
		AccusedKSUID:    complaint.AccusedKSUID,
		ContentKSUID:    complaint.ContentKSUID,
		Type:            complaint.Type,
		Status:          complaint.Status,
		Title:           complaint.Title,
		Description:     complaint.Description,
		CreatedAt:       complaint.CreatedAt,
		EventType:       "complaint.created",
		Timestamp:       time.Now(),
	}

	return p.publishEvent("complaint.created", event)
}

// PublishComplaintProcessed 发布投诉处理事件
func (p *complaintPublisher) PublishComplaintProcessed(ctx context.Context, complaint *model.Complaint) error {
	processedAt := time.Now()
	if complaint.ProcessedAt != nil {
		processedAt = *complaint.ProcessedAt
	}

	event := ComplaintProcessedEvent{
		ComplaintKSUID:  complaint.ComplaintKSUID,
		ComplainerKSUID: complaint.ComplainerKSUID,
		AccusedKSUID:    complaint.AccusedKSUID,
		ContentKSUID:    complaint.ContentKSUID,
		Type:            complaint.Type,
		Status:          complaint.Status,
		ProcessNote:     complaint.ProcessNote,
		ProcessorKSUID:  complaint.ProcessorKSUID,
		ProcessedAt:     processedAt,
		EventType:       "complaint.processed",
		Timestamp:       time.Now(),
	}

	return p.publishEvent("complaint.processed", event)
}

// PublishIdentityVerificationUpdated 发布身份认证事件
func (p *complaintPublisher) PublishIdentityVerificationUpdated(ctx context.Context, verification *model.IdentityVerification) error {
	reviewedAt := time.Now()
	if verification.ReviewedAt != nil {
		reviewedAt = *verification.ReviewedAt
	}

	event := IdentityVerificationUpdatedEvent{
		VerificationKSUID: verification.VerificationKSUID,
		UserKSUID:         verification.UserKSUID,
		Type:              verification.Type,
		Status:            verification.Status,
		ReviewerKSUID:     verification.ReviewerKSUID,
		ReviewNote:        verification.ReviewNote,
		ReviewedAt:        reviewedAt,
		EventType:         "identity.verification.updated",
		Timestamp:         time.Now(),
	}

	return p.publishEvent("identity.verification.updated", event)
}

// PublishRightsVerificationUpdated 发布权益认证事件
func (p *complaintPublisher) PublishRightsVerificationUpdated(ctx context.Context, verification *model.RightsVerification) error {
	reviewedAt := time.Now()
	if verification.ReviewedAt != nil {
		reviewedAt = *verification.ReviewedAt
	}

	event := RightsVerificationUpdatedEvent{
		RightsKSUID:   verification.RightsKSUID,
		UserKSUID:     verification.UserKSUID,
		Type:          verification.Type,
		Status:        verification.Status,
		ReviewerKSUID: verification.ReviewerKSUID,
		ReviewNote:    verification.ReviewNote,
		ReviewedAt:    reviewedAt,
		EventType:     "rights.verification.updated",
		Timestamp:     time.Now(),
	}

	return p.publishEvent("rights.verification.updated", event)
}

// publishEvent 发布事件
func (p *complaintPublisher) publishEvent(routingKey string, event interface{}) error {
	body, err := json.Marshal(event)
	if err != nil {
		log.Error().Err(err).Str("routing_key", routingKey).Msg("序列化事件失败")
		return err
	}

	err = p.channel.Publish(
		p.exchange,
		routingKey,
		false, // mandatory
		false, // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			Timestamp:    time.Now(),
			DeliveryMode: amqp.Persistent, // 持久化消息
		},
	)

	if err != nil {
		log.Error().Err(err).Str("routing_key", routingKey).Msg("发布事件失败")
		return err
	}

	log.Debug().Str("routing_key", routingKey).Msg("事件发布成功")
	return nil
}

// Close 关闭发布者
func (p *complaintPublisher) Close() error {
	if p.channel != nil {
		return p.channel.Close()
	}
	return nil
}
