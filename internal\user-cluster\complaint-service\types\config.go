package types

import (
	globalTypes "pxpat-backend/pkg/types"
	"time"
)

// Config 投诉服务配置结构
type Config struct {
	Server   globalTypes.GlobalServerConfig   `mapstructure:"server"`
	Database globalTypes.GlobalDatabaseConfig `mapstructure:"database"`
	Redis    globalTypes.GlobalRedisConfig    `mapstructure:"redis"`
	RabbitMQ globalTypes.GlobalRabbitMQConfig `mapstructure:"rabbitmq"`
	JWT      globalTypes.GlobalJWTConfig      `mapstructure:"jwt"`
	Log      globalTypes.GlobalLogConfig      `mapstructure:"log"`
	Consul   globalTypes.GlobalConsulConfig   `mapstructure:"consul"`
	OTLP     globalTypes.GlobalOTLPConfig     `mapstructure:"otlp"`

	// 业务配置
	Complaint ComplaintConfig `mapstructure:"complaint"`
	Storage   StorageConfig   `mapstructure:"storage"`
	Security  SecurityConfig  `mapstructure:"security"`
	UserService UserServiceConfig `mapstructure:"user_service"`
}

// ComplaintConfig 投诉配置
type ComplaintConfig struct {
	Limits      LimitsConfig      `mapstructure:"limits"`
	Cache       CacheConfig       `mapstructure:"cache"`
	FileUpload  FileUploadConfig  `mapstructure:"file_upload"`
	Verification VerificationConfig `mapstructure:"verification"`
}

// LimitsConfig 限制配置
type LimitsConfig struct {
	TitleMaxLength       int `mapstructure:"title_max_length"`       // 标题最大长度
	DescriptionMaxLength int `mapstructure:"description_max_length"` // 描述最大长度
	MaxEvidenceFiles     int `mapstructure:"max_evidence_files"`     // 最大证据文件数
	MaxComplaintsPerDay  int `mapstructure:"max_complaints_per_day"` // 每日最大投诉数
	MaxRightsPerUser     int `mapstructure:"max_rights_per_user"`    // 每用户最大权益认证数
}

// CacheConfig 缓存配置
type CacheConfig struct {
	ComplaintTTL     time.Duration `mapstructure:"complaint_ttl"`      // 投诉缓存TTL
	IdentityTTL      time.Duration `mapstructure:"identity_ttl"`       // 身份认证缓存TTL
	RightsTTL        time.Duration `mapstructure:"rights_ttl"`         // 权益认证缓存TTL
	CountryTTL       time.Duration `mapstructure:"country_ttl"`        // 国家地区缓存TTL
	TrademarkTTL     time.Duration `mapstructure:"trademark_ttl"`      // 商标类型缓存TTL
	ViolationTTL     time.Duration `mapstructure:"violation_ttl"`      // 违规类型缓存TTL
}

// FileUploadConfig 文件上传配置
type FileUploadConfig struct {
	MaxFileSize      int64    `mapstructure:"max_file_size"`      // 最大文件大小(字节)
	AllowedTypes     []string `mapstructure:"allowed_types"`      // 允许的文件类型
	MaxFilesPerUpload int     `mapstructure:"max_files_per_upload"` // 单次上传最大文件数
	StoragePath      string   `mapstructure:"storage_path"`       // 存储路径前缀
}

// VerificationConfig 认证配置
type VerificationConfig struct {
	AutoApprove          bool          `mapstructure:"auto_approve"`           // 是否自动审批
	ReviewTimeout        time.Duration `mapstructure:"review_timeout"`         // 审核超时时间
	CertificateValidDays int           `mapstructure:"certificate_valid_days"` // 证书有效天数
	RequireManualReview  bool          `mapstructure:"require_manual_review"`  // 是否需要人工审核
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	Cors            globalTypes.GlobalCORSConfig `mapstructure:"cors"`
	RateLimit       RateLimitConfig              `mapstructure:"rate_limit"`
	IPWhitelist     []string                     `mapstructure:"ip_whitelist"`     // IP白名单
	IPBlacklist     []string                     `mapstructure:"ip_blacklist"`     // IP黑名单
	EnableIPFilter  bool                         `mapstructure:"enable_ip_filter"` // 是否启用IP过滤
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enable           bool          `mapstructure:"enable"`             // 是否启用限流
	RequestsPerMinute int          `mapstructure:"requests_per_minute"` // 每分钟请求数
	BurstSize        int           `mapstructure:"burst_size"`          // 突发大小
	WindowSize       time.Duration `mapstructure:"window_size"`         // 时间窗口大小
}

// StorageConfig 存储配置
type StorageConfig struct {
	Minio globalTypes.GlobalStorageConfig `mapstructure:"minio"`
}

// UserServiceConfig 用户服务配置
type UserServiceConfig struct {
	BaseURL string        `mapstructure:"base_url"` // 用户服务基础URL
	Timeout time.Duration `mapstructure:"timeout"`  // 请求超时时间
	APIKey  string        `mapstructure:"api_key"`  // API密钥
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	Email EmailNotificationConfig `mapstructure:"email"`
	SMS   SMSNotificationConfig   `mapstructure:"sms"`
}

// EmailNotificationConfig 邮件通知配置
type EmailNotificationConfig struct {
	Enable   bool   `mapstructure:"enable"`
	SMTPHost string `mapstructure:"smtp_host"`
	SMTPPort int    `mapstructure:"smtp_port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	FromAddr string `mapstructure:"from_addr"`
}

// SMSNotificationConfig 短信通知配置
type SMSNotificationConfig struct {
	Enable    bool   `mapstructure:"enable"`
	Provider  string `mapstructure:"provider"`  // 短信服务提供商
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
	SignName  string `mapstructure:"sign_name"` // 短信签名
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Metrics MetricsConfig `mapstructure:"metrics"`
	Tracing TracingConfig `mapstructure:"tracing"`
	Health  HealthConfig  `mapstructure:"health"`
}

// MetricsConfig 指标配置
type MetricsConfig struct {
	Enable   bool   `mapstructure:"enable"`
	Endpoint string `mapstructure:"endpoint"`
	Interval time.Duration `mapstructure:"interval"`
}

// TracingConfig 链路追踪配置
type TracingConfig struct {
	Enable     bool    `mapstructure:"enable"`
	Endpoint   string  `mapstructure:"endpoint"`
	SampleRate float64 `mapstructure:"sample_rate"`
}

// HealthConfig 健康检查配置
type HealthConfig struct {
	Enable   bool          `mapstructure:"enable"`
	Interval time.Duration `mapstructure:"interval"`
	Timeout  time.Duration `mapstructure:"timeout"`
}
