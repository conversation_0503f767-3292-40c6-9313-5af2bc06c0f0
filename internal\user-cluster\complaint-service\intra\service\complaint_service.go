package service

import (
	"context"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
)

// InternalComplaintService 内部投诉服务接口
type InternalComplaintService interface {
	// 获取用户投诉统计
	GetUserComplaintStats(ctx context.Context, userKSUID string) (*UserComplaintStats, error)
	
	// 获取内容投诉统计
	GetContentComplaintStats(ctx context.Context, contentKSUID string) (*ContentComplaintStats, error)
	
	// 批量获取内容投诉状态
	BatchGetContentComplaintStatus(ctx context.Context, contentKSUIDs []string) (map[string]*ContentComplaintStatus, error)
	
	// 检查用户是否可以投诉
	CanUserComplain(ctx context.Context, userKSUID string) (*UserComplaintPermission, error)
	
	// 获取投诉处理结果
	GetComplaintResult(ctx context.Context, complaintKSUID string) (*ComplaintResult, error)
}

// UserComplaintStats 用户投诉统计
type UserComplaintStats struct {
	UserKSUID           string `json:"user_ksuid"`
	TotalComplaints     int64  `json:"total_complaints"`     // 总投诉数
	PendingComplaints   int64  `json:"pending_complaints"`   // 待处理投诉数
	ApprovedComplaints  int64  `json:"approved_complaints"`  // 已通过投诉数
	RejectedComplaints  int64  `json:"rejected_complaints"`  // 已拒绝投诉数
	TodayComplaints     int64  `json:"today_complaints"`     // 今日投诉数
	ComplaintsAgainstMe int64  `json:"complaints_against_me"` // 针对我的投诉数
}

// ContentComplaintStats 内容投诉统计
type ContentComplaintStats struct {
	ContentKSUID       string `json:"content_ksuid"`
	TotalComplaints    int64  `json:"total_complaints"`    // 总投诉数
	PendingComplaints  int64  `json:"pending_complaints"`  // 待处理投诉数
	ApprovedComplaints int64  `json:"approved_complaints"` // 已通过投诉数
	RejectedComplaints int64  `json:"rejected_complaints"` // 已拒绝投诉数
	IsBlocked          bool   `json:"is_blocked"`          // 是否被封禁
	BlockReason        string `json:"block_reason"`        // 封禁原因
}

// ContentComplaintStatus 内容投诉状态
type ContentComplaintStatus struct {
	ContentKSUID    string                `json:"content_ksuid"`
	HasComplaints   bool                  `json:"has_complaints"`   // 是否有投诉
	ComplaintCount  int64                 `json:"complaint_count"`  // 投诉数量
	Status          model.ComplaintStatus `json:"status"`           // 最新投诉状态
	IsBlocked       bool                  `json:"is_blocked"`       // 是否被封禁
	LastComplaintAt string                `json:"last_complaint_at"` // 最后投诉时间
}

// UserComplaintPermission 用户投诉权限
type UserComplaintPermission struct {
	UserKSUID           string `json:"user_ksuid"`
	CanComplain         bool   `json:"can_complain"`          // 是否可以投诉
	RemainingComplaints int    `json:"remaining_complaints"`  // 剩余投诉次数
	NextComplaintTime   string `json:"next_complaint_time"`   // 下次可投诉时间
	Reason              string `json:"reason"`                // 不能投诉的原因
}

// ComplaintResult 投诉处理结果
type ComplaintResult struct {
	ComplaintKSUID string                `json:"complaint_ksuid"`
	Status         model.ComplaintStatus `json:"status"`
	Result         string                `json:"result"`         // 处理结果
	ProcessNote    string                `json:"process_note"`   // 处理说明
	ProcessedAt    string                `json:"processed_at"`   // 处理时间
	ProcessorKSUID string                `json:"processor_ksuid"` // 处理人KSUID
}

// internalComplaintService 内部投诉服务实现
type internalComplaintService struct {
	complaintRepo         repository.ComplaintRepository
	complaintEvidenceRepo repository.ComplaintEvidenceRepository
	violationCategoryRepo repository.ViolationCategoryRepository
}

// NewInternalComplaintService 创建内部投诉服务实例
func NewInternalComplaintService(
	complaintRepo repository.ComplaintRepository,
	complaintEvidenceRepo repository.ComplaintEvidenceRepository,
	violationCategoryRepo repository.ViolationCategoryRepository,
) InternalComplaintService {
	return &internalComplaintService{
		complaintRepo:         complaintRepo,
		complaintEvidenceRepo: complaintEvidenceRepo,
		violationCategoryRepo: violationCategoryRepo,
	}
}

// GetUserComplaintStats 获取用户投诉统计
func (s *internalComplaintService) GetUserComplaintStats(ctx context.Context, userKSUID string) (*UserComplaintStats, error) {
	// 获取用户总投诉数
	totalComplaints, err := s.complaintRepo.CountByComplainerKSUID(ctx, userKSUID)
	if err != nil {
		return nil, err
	}

	// 获取待处理投诉数
	pendingComplaints, err := s.complaintRepo.CountByComplainerKSUIDAndStatus(ctx, userKSUID, model.ComplaintStatusPending)
	if err != nil {
		return nil, err
	}

	// 获取已通过投诉数
	approvedComplaints, err := s.complaintRepo.CountByComplainerKSUIDAndStatus(ctx, userKSUID, model.ComplaintStatusApproved)
	if err != nil {
		return nil, err
	}

	// 获取已拒绝投诉数
	rejectedComplaints, err := s.complaintRepo.CountByComplainerKSUIDAndStatus(ctx, userKSUID, model.ComplaintStatusRejected)
	if err != nil {
		return nil, err
	}

	// 获取今日投诉数
	todayComplaints, err := s.complaintRepo.CountTodayComplaintsByUser(ctx, userKSUID)
	if err != nil {
		return nil, err
	}

	// 获取针对该用户的投诉数
	complaintsAgainstMe, err := s.complaintRepo.CountByAccusedKSUID(ctx, userKSUID)
	if err != nil {
		return nil, err
	}

	return &UserComplaintStats{
		UserKSUID:           userKSUID,
		TotalComplaints:     totalComplaints,
		PendingComplaints:   pendingComplaints,
		ApprovedComplaints:  approvedComplaints,
		RejectedComplaints:  rejectedComplaints,
		TodayComplaints:     todayComplaints,
		ComplaintsAgainstMe: complaintsAgainstMe,
	}, nil
}

// GetContentComplaintStats 获取内容投诉统计
func (s *internalComplaintService) GetContentComplaintStats(ctx context.Context, contentKSUID string) (*ContentComplaintStats, error) {
	// 获取内容总投诉数
	totalComplaints, err := s.complaintRepo.CountByContentKSUID(ctx, contentKSUID)
	if err != nil {
		return nil, err
	}

	// 获取待处理投诉数
	pendingComplaints, err := s.complaintRepo.CountByContentKSUIDAndStatus(ctx, contentKSUID, model.ComplaintStatusPending)
	if err != nil {
		return nil, err
	}

	// 获取已通过投诉数
	approvedComplaints, err := s.complaintRepo.CountByContentKSUIDAndStatus(ctx, contentKSUID, model.ComplaintStatusApproved)
	if err != nil {
		return nil, err
	}

	// 获取已拒绝投诉数
	rejectedComplaints, err := s.complaintRepo.CountByContentKSUIDAndStatus(ctx, contentKSUID, model.ComplaintStatusRejected)
	if err != nil {
		return nil, err
	}

	// 检查内容是否被封禁
	isBlocked := approvedComplaints > 0
	blockReason := ""
	if isBlocked {
		// 获取最新的已通过投诉，获取封禁原因
		complaints, _, err := s.complaintRepo.GetByContentKSUIDAndStatus(ctx, contentKSUID, model.ComplaintStatusApproved, 1, 1)
		if err == nil && len(complaints) > 0 {
			blockReason = complaints[0].ProcessNote
		}
	}

	return &ContentComplaintStats{
		ContentKSUID:       contentKSUID,
		TotalComplaints:    totalComplaints,
		PendingComplaints:  pendingComplaints,
		ApprovedComplaints: approvedComplaints,
		RejectedComplaints: rejectedComplaints,
		IsBlocked:          isBlocked,
		BlockReason:        blockReason,
	}, nil
}

// BatchGetContentComplaintStatus 批量获取内容投诉状态
func (s *internalComplaintService) BatchGetContentComplaintStatus(ctx context.Context, contentKSUIDs []string) (map[string]*ContentComplaintStatus, error) {
	result := make(map[string]*ContentComplaintStatus)

	for _, contentKSUID := range contentKSUIDs {
		stats, err := s.GetContentComplaintStats(ctx, contentKSUID)
		if err != nil {
			return nil, err
		}

		// 获取最新投诉
		complaints, _, err := s.complaintRepo.GetByContentKSUID(ctx, contentKSUID, 1, 1)
		var lastComplaintAt string
		var status model.ComplaintStatus
		if err == nil && len(complaints) > 0 {
			lastComplaintAt = complaints[0].CreatedAt.Format("2006-01-02 15:04:05")
			status = complaints[0].Status
		}

		result[contentKSUID] = &ContentComplaintStatus{
			ContentKSUID:    contentKSUID,
			HasComplaints:   stats.TotalComplaints > 0,
			ComplaintCount:  stats.TotalComplaints,
			Status:          status,
			IsBlocked:       stats.IsBlocked,
			LastComplaintAt: lastComplaintAt,
		}
	}

	return result, nil
}

// CanUserComplain 检查用户是否可以投诉
func (s *internalComplaintService) CanUserComplain(ctx context.Context, userKSUID string) (*UserComplaintPermission, error) {
	// 获取今日投诉数
	todayComplaints, err := s.complaintRepo.CountTodayComplaintsByUser(ctx, userKSUID)
	if err != nil {
		return nil, err
	}

	// 假设每日最大投诉数为10
	maxComplaintsPerDay := int64(10)
	canComplain := todayComplaints < maxComplaintsPerDay
	remainingComplaints := int(maxComplaintsPerDay - todayComplaints)
	if remainingComplaints < 0 {
		remainingComplaints = 0
	}

	reason := ""
	nextComplaintTime := ""
	if !canComplain {
		reason = "今日投诉次数已达上限"
		nextComplaintTime = "明日00:00:00"
	}

	return &UserComplaintPermission{
		UserKSUID:           userKSUID,
		CanComplain:         canComplain,
		RemainingComplaints: remainingComplaints,
		NextComplaintTime:   nextComplaintTime,
		Reason:              reason,
	}, nil
}

// GetComplaintResult 获取投诉处理结果
func (s *internalComplaintService) GetComplaintResult(ctx context.Context, complaintKSUID string) (*ComplaintResult, error) {
	complaint, err := s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		return nil, err
	}

	result := ""
	switch complaint.Status {
	case model.ComplaintStatusApproved:
		result = "投诉成立，内容已处理"
	case model.ComplaintStatusRejected:
		result = "投诉不成立"
	case model.ComplaintStatusPending:
		result = "投诉处理中"
	}

	processedAt := ""
	if complaint.ProcessedAt != nil {
		processedAt = complaint.ProcessedAt.Format("2006-01-02 15:04:05")
	}

	return &ComplaintResult{
		ComplaintKSUID: complaintKSUID,
		Status:         complaint.Status,
		Result:         result,
		ProcessNote:    complaint.ProcessNote,
		ProcessedAt:    processedAt,
		ProcessorKSUID: complaint.ProcessorKSUID,
	}, nil
}
