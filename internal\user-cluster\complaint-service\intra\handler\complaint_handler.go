package handler

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/intra/service"
)

// InternalComplaintHandler 内部投诉处理器
type InternalComplaintHandler struct {
	internalComplaintService service.InternalComplaintService
}

// NewInternalComplaintHandler 创建内部投诉处理器实例
func NewInternalComplaintHandler(internalComplaintService service.InternalComplaintService) *InternalComplaintHandler {
	return &InternalComplaintHandler{
		internalComplaintService: internalComplaintService,
	}
}

// GetUserComplaintStats 获取用户投诉统计
// @Summary 获取用户投诉统计
// @Description 获取指定用户的投诉统计信息
// @Tags 内部接口-投诉
// @Accept json
// @Produce json
// @Param user_ksuid path string true "用户KSUID"
// @Success 200 {object} service.UserComplaintStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /internal/v1/complaints/users/{user_ksuid}/stats [get]
func (h *InternalComplaintHandler) GetUserComplaintStats(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户KSUID不能为空",
		})
		return
	}

	stats, err := h.internalComplaintService.GetUserComplaintStats(c.Request.Context(), userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取用户投诉统计失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取用户投诉统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetContentComplaintStats 获取内容投诉统计
// @Summary 获取内容投诉统计
// @Description 获取指定内容的投诉统计信息
// @Tags 内部接口-投诉
// @Accept json
// @Produce json
// @Param content_ksuid path string true "内容KSUID"
// @Success 200 {object} service.ContentComplaintStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /internal/v1/complaints/contents/{content_ksuid}/stats [get]
func (h *InternalComplaintHandler) GetContentComplaintStats(c *gin.Context) {
	contentKSUID := c.Param("content_ksuid")
	if contentKSUID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "内容KSUID不能为空",
		})
		return
	}

	stats, err := h.internalComplaintService.GetContentComplaintStats(c.Request.Context(), contentKSUID)
	if err != nil {
		log.Error().Err(err).Str("content_ksuid", contentKSUID).Msg("获取内容投诉统计失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取内容投诉统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// BatchGetContentComplaintStatus 批量获取内容投诉状态
// @Summary 批量获取内容投诉状态
// @Description 批量获取多个内容的投诉状态信息
// @Tags 内部接口-投诉
// @Accept json
// @Produce json
// @Param content_ksuids query string true "内容KSUID列表，逗号分隔"
// @Success 200 {object} map[string]service.ContentComplaintStatus
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /internal/v1/complaints/contents/batch-status [get]
func (h *InternalComplaintHandler) BatchGetContentComplaintStatus(c *gin.Context) {
	contentKSUIDsStr := c.Query("content_ksuids")
	if contentKSUIDsStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "内容KSUID列表不能为空",
		})
		return
	}

	contentKSUIDs := strings.Split(contentKSUIDsStr, ",")
	if len(contentKSUIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "内容KSUID列表不能为空",
		})
		return
	}

	// 限制批量查询数量
	if len(contentKSUIDs) > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "批量查询数量不能超过100个",
		})
		return
	}

	statusMap, err := h.internalComplaintService.BatchGetContentComplaintStatus(c.Request.Context(), contentKSUIDs)
	if err != nil {
		log.Error().Err(err).Strs("content_ksuids", contentKSUIDs).Msg("批量获取内容投诉状态失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "批量获取内容投诉状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, statusMap)
}

// CanUserComplain 检查用户是否可以投诉
// @Summary 检查用户投诉权限
// @Description 检查指定用户是否可以进行投诉
// @Tags 内部接口-投诉
// @Accept json
// @Produce json
// @Param user_ksuid path string true "用户KSUID"
// @Success 200 {object} service.UserComplaintPermission
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /internal/v1/complaints/users/{user_ksuid}/permission [get]
func (h *InternalComplaintHandler) CanUserComplain(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户KSUID不能为空",
		})
		return
	}

	permission, err := h.internalComplaintService.CanUserComplain(c.Request.Context(), userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("检查用户投诉权限失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "检查用户投诉权限失败",
		})
		return
	}

	c.JSON(http.StatusOK, permission)
}

// GetComplaintResult 获取投诉处理结果
// @Summary 获取投诉处理结果
// @Description 获取指定投诉的处理结果
// @Tags 内部接口-投诉
// @Accept json
// @Produce json
// @Param complaint_ksuid path string true "投诉KSUID"
// @Success 200 {object} service.ComplaintResult
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /internal/v1/complaints/{complaint_ksuid}/result [get]
func (h *InternalComplaintHandler) GetComplaintResult(c *gin.Context) {
	complaintKSUID := c.Param("complaint_ksuid")
	if complaintKSUID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "投诉KSUID不能为空",
		})
		return
	}

	result, err := h.internalComplaintService.GetComplaintResult(c.Request.Context(), complaintKSUID)
	if err != nil {
		log.Error().Err(err).Str("complaint_ksuid", complaintKSUID).Msg("获取投诉处理结果失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取投诉处理结果失败",
		})
		return
	}

	c.JSON(http.StatusOK, result)
}
