package impl

import (
	"context"
	"errors"
	"strings"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// countryRepo 国家地区数据访问层实现
type countryRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewCountryRepository 创建国家地区数据访问层实例
func NewCountryRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.CountryRepository {
	return &countryRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *countryRepo) GetDB() interface{} {
	return r.db
}

// Create 创建国家地区
func (r *countryRepo) Create(ctx context.Context, country *model.Country) error {
	log.Debug().
		Str("country_code", country.Code).
		Str("country_name", country.Name).
		Msg("开始创建国家地区记录")

	err := r.db.WithContext(ctx).Create(country).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("country_code", country.Code).
			Msg("创建国家地区记录失败")
		return err
	}

	log.Debug().
		Str("country_code", country.Code).
		Msg("创建国家地区记录成功")

	return nil
}

// GetByCode 根据代码获取国家地区
func (r *countryRepo) GetByCode(ctx context.Context, code string) (*model.Country, error) {
	var country model.Country
	err := r.db.WithContext(ctx).
		Where("code = ?", code).
		First(&country).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrCountryNotFound
		}
		return nil, err
	}

	return &country, nil
}

// GetByID 根据ID获取国家地区
func (r *countryRepo) GetByID(ctx context.Context, id uint) (*model.Country, error) {
	var country model.Country
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&country).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrCountryNotFound
		}
		return nil, err
	}

	return &country, nil
}

// Update 更新国家地区
func (r *countryRepo) Update(ctx context.Context, country *model.Country) error {
	log.Debug().
		Str("country_code", country.Code).
		Msg("开始更新国家地区记录")

	err := r.db.WithContext(ctx).Save(country).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("country_code", country.Code).
			Msg("更新国家地区记录失败")
		return err
	}

	log.Debug().
		Str("country_code", country.Code).
		Msg("更新国家地区记录成功")

	return nil
}

// Delete 删除国家地区
func (r *countryRepo) Delete(ctx context.Context, code string) error {
	log.Debug().
		Str("country_code", code).
		Msg("开始删除国家地区记录")

	err := r.db.WithContext(ctx).
		Where("code = ?", code).
		Delete(&model.Country{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("country_code", code).
			Msg("删除国家地区记录失败")
		return err
	}

	log.Debug().
		Str("country_code", code).
		Msg("删除国家地区记录成功")

	return nil
}

// GetAll 获取所有国家地区
func (r *countryRepo) GetAll(ctx context.Context) ([]*model.Country, error) {
	var countries []*model.Country
	err := r.db.WithContext(ctx).
		Order("sort_order ASC, name ASC").
		Find(&countries).Error

	if err != nil {
		return nil, err
	}

	return countries, nil
}

// GetByContinent 根据大洲获取国家地区列表
func (r *countryRepo) GetByContinent(ctx context.Context, continent string) ([]*model.Country, error) {
	var countries []*model.Country
	err := r.db.WithContext(ctx).
		Where("continent = ?", continent).
		Order("sort_order ASC, name ASC").
		Find(&countries).Error

	if err != nil {
		return nil, err
	}

	return countries, nil
}

// GetByRegion 根据地区获取国家地区列表
func (r *countryRepo) GetByRegion(ctx context.Context, region string) ([]*model.Country, error) {
	var countries []*model.Country
	err := r.db.WithContext(ctx).
		Where("region = ?", region).
		Order("sort_order ASC, name ASC").
		Find(&countries).Error

	if err != nil {
		return nil, err
	}

	return countries, nil
}

// GetActive 获取启用的国家地区列表
func (r *countryRepo) GetActive(ctx context.Context) ([]*model.Country, error) {
	var countries []*model.Country
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("sort_order ASC, name ASC").
		Find(&countries).Error

	if err != nil {
		return nil, err
	}

	return countries, nil
}

// Search 搜索国家地区
func (r *countryRepo) Search(ctx context.Context, keyword string) ([]*model.Country, error) {
	var countries []*model.Country
	keyword = "%" + strings.ToLower(keyword) + "%"
	
	err := r.db.WithContext(ctx).
		Where("LOWER(name) LIKE ? OR LOWER(name_en) LIKE ? OR LOWER(name_local) LIKE ? OR LOWER(code) LIKE ?", 
			keyword, keyword, keyword, keyword).
		Order("sort_order ASC, name ASC").
		Find(&countries).Error

	if err != nil {
		return nil, err
	}

	return countries, nil
}

// ExistsByCode 检查国家代码是否存在
func (r *countryRepo) ExistsByCode(ctx context.Context, code string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Country{}).
		Where("code = ?", code).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdateStatus 更新国家地区状态
func (r *countryRepo) UpdateStatus(ctx context.Context, code string, isActive bool) error {
	err := r.db.WithContext(ctx).
		Model(&model.Country{}).
		Where("code = ?", code).
		Update("is_active", isActive).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("country_code", code).
			Bool("is_active", isActive).
			Msg("更新国家地区状态失败")
		return err
	}

	return nil
}

// GetMaxSortOrder 获取最大排序值
func (r *countryRepo) GetMaxSortOrder(ctx context.Context) (int, error) {
	var maxOrder int
	err := r.db.WithContext(ctx).
		Model(&model.Country{}).
		Select("COALESCE(MAX(sort_order), 0)").
		Scan(&maxOrder).Error
	return maxOrder, err
}

// GetChinaRegions 获取中国特别行政区
func (r *countryRepo) GetChinaRegions(ctx context.Context) ([]*model.Country, error) {
	var countries []*model.Country
	err := r.db.WithContext(ctx).
		Where("code IN ?", []string{"CN", "HK", "MO", "TW"}).
		Order("sort_order ASC, name ASC").
		Find(&countries).Error

	if err != nil {
		return nil, err
	}

	return countries, nil
}
