package identity

import (
	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
)

// RegisterIdentityRoutes 注册身份认证相关路由
func RegisterIdentityRoutes(r *gin.RouterGroup, identityHandler *handler.IdentityHandler, authMiddleware gin.HandlerFunc) {
	// 身份认证路由组
	identityGroup := r.Group("/identity")
	{
		// 身份认证管理路由（需要认证）
		verificationGroup := identityGroup.Group("/verification")
		verificationGroup.Use(authMiddleware)
		{
			verificationGroup.POST("", identityHandler.CreateIdentityVerification)                    // 创建身份认证
			verificationGroup.GET("/:verification_ksuid", identityHandler.GetIdentityVerification)    // 获取身份认证详情
			verificationGroup.PUT("/:verification_ksuid", identityHandler.UpdateIdentityVerification) // 更新身份认证
			verificationGroup.GET("/my", identityHandler.GetMyIdentityVerification)                   // 获取我的身份认证
		}

		// 公开路由（不需要认证）
		identityGroup.GET("/countries", identityHandler.GetCountries)                           // 获取国家地区列表
		identityGroup.GET("/china-regions", identityHandler.GetChinaRegions)                   // 获取中国特别行政区
		identityGroup.GET("/trademark-categories", identityHandler.GetTrademarkCategories)     // 获取商标类型列表
		identityGroup.GET("/countries/search", identityHandler.SearchCountries)                // 搜索国家地区
		identityGroup.GET("/trademark-categories/search", identityHandler.SearchTrademarkCategories) // 搜索商标类型
	}
}
