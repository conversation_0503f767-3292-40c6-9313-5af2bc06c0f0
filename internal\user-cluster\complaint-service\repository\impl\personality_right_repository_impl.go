package impl

import (
	"context"
	"errors"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// personalityRightRepo 人格权数据访问层实现
type personalityRightRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewPersonalityRightRepository 创建人格权数据访问层实例
func NewPersonalityRightRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.PersonalityRightRepository {
	return &personalityRightRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *personalityRightRepo) GetDB() interface{} {
	return r.db
}

// Create 创建人格权
func (r *personalityRightRepo) Create(ctx context.Context, personalityRight *model.PersonalityRight) error {
	log.Debug().
		Str("rights_ksuid", personalityRight.RightsKSUID).
		Str("personality_rights_type", string(personalityRight.PersonalityRightsType)).
		Msg("开始创建人格权记录")

	err := r.db.WithContext(ctx).Create(personalityRight).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("rights_ksuid", personalityRight.RightsKSUID).
			Str("personality_rights_type", string(personalityRight.PersonalityRightsType)).
			Msg("创建人格权记录失败")
		return err
	}

	log.Debug().
		Str("rights_ksuid", personalityRight.RightsKSUID).
		Str("personality_rights_type", string(personalityRight.PersonalityRightsType)).
		Msg("创建人格权记录成功")

	return nil
}

// GetByID 根据ID获取人格权
func (r *personalityRightRepo) GetByID(ctx context.Context, id uint) (*model.PersonalityRight, error) {
	var personalityRight model.PersonalityRight
	err := r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("id = ?", id).
		First(&personalityRight).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrPersonalityRightNotFound
		}
		return nil, err
	}

	return &personalityRight, nil
}

// Update 更新人格权
func (r *personalityRightRepo) Update(ctx context.Context, personalityRight *model.PersonalityRight) error {
	log.Debug().
		Uint("personality_right_id", personalityRight.ID).
		Str("personality_rights_type", string(personalityRight.PersonalityRightsType)).
		Msg("开始更新人格权记录")

	err := r.db.WithContext(ctx).Save(personalityRight).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint("personality_right_id", personalityRight.ID).
			Msg("更新人格权记录失败")
		return err
	}

	log.Debug().
		Uint("personality_right_id", personalityRight.ID).
		Msg("更新人格权记录成功")

	return nil
}

// Delete 删除人格权
func (r *personalityRightRepo) Delete(ctx context.Context, id uint) error {
	log.Debug().
		Uint("personality_right_id", id).
		Msg("开始删除人格权记录")

	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		Delete(&model.PersonalityRight{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Uint("personality_right_id", id).
			Msg("删除人格权记录失败")
		return err
	}

	log.Debug().
		Uint("personality_right_id", id).
		Msg("删除人格权记录成功")

	return nil
}

// GetByRightsKSUID 根据权益认证KSUID获取人格权列表
func (r *personalityRightRepo) GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.PersonalityRight, error) {
	var personalityRights []*model.PersonalityRight
	err := r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("rights_ksuid = ?", rightsKSUID).
		Order("created_at ASC").
		Find(&personalityRights).Error

	if err != nil {
		return nil, err
	}

	return personalityRights, nil
}

// GetByType 根据人格权类型获取人格权列表
func (r *personalityRightRepo) GetByType(ctx context.Context, personalityRightsType model.PersonalityRightsType) ([]*model.PersonalityRight, error) {
	var personalityRights []*model.PersonalityRight
	err := r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("personality_rights_type = ?", personalityRightsType).
		Order("created_at DESC").
		Find(&personalityRights).Error

	if err != nil {
		return nil, err
	}

	return personalityRights, nil
}

// BatchCreate 批量创建人格权
func (r *personalityRightRepo) BatchCreate(ctx context.Context, personalityRights []*model.PersonalityRight) error {
	if len(personalityRights) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(personalityRights)).
		Msg("开始批量创建人格权记录")

	err := r.db.WithContext(ctx).CreateInBatches(personalityRights, 100).Error
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(personalityRights)).
			Msg("批量创建人格权记录失败")
		return err
	}

	log.Debug().
		Int("count", len(personalityRights)).
		Msg("批量创建人格权记录成功")

	return nil
}

// BatchDelete 批量删除人格权
func (r *personalityRightRepo) BatchDelete(ctx context.Context, rightsKSUID string) error {
	log.Debug().
		Str("rights_ksuid", rightsKSUID).
		Msg("开始批量删除人格权记录")

	err := r.db.WithContext(ctx).
		Where("rights_ksuid = ?", rightsKSUID).
		Delete(&model.PersonalityRight{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("rights_ksuid", rightsKSUID).
			Msg("批量删除人格权记录失败")
		return err
	}

	log.Debug().
		Str("rights_ksuid", rightsKSUID).
		Msg("批量删除人格权记录成功")

	return nil
}
