package utils

import (
	"crypto/md5"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"
)

// FileInfo 文件信息
type FileInfo struct {
	OriginalName string `json:"original_name"` // 原始文件名
	FileName     string `json:"file_name"`     // 存储文件名
	FileSize     int64  `json:"file_size"`     // 文件大小
	FileType     string `json:"file_type"`     // 文件类型
	FilePath     string `json:"file_path"`     // 文件路径
	FileHash     string `json:"file_hash"`     // 文件哈希
	UploadTime   string `json:"upload_time"`   // 上传时间
}

// AllowedFileTypes 允许的文件类型
var AllowedFileTypes = map[string][]string{
	"image": {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"},
	"document": {".pdf", ".doc", ".docx", ".txt", ".rtf"},
	"archive": {".zip", ".rar", ".7z", ".tar", ".gz"},
	"video": {".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv"},
	"audio": {".mp3", ".wav", ".flac", ".aac", ".ogg"},
}

// MaxFileSizes 最大文件大小限制（字节）
var MaxFileSizes = map[string]int64{
	"image":    10 * 1024 * 1024,  // 10MB
	"document": 20 * 1024 * 1024,  // 20MB
	"archive":  50 * 1024 * 1024,  // 50MB
	"video":    100 * 1024 * 1024, // 100MB
	"audio":    50 * 1024 * 1024,  // 50MB
}

// ValidateFile 验证文件
func ValidateFile(fileHeader *multipart.FileHeader, fileCategory string) (bool, string) {
	if fileHeader == nil {
		return false, "文件不能为空"
	}

	// 检查文件大小
	maxSize, exists := MaxFileSizes[fileCategory]
	if !exists {
		maxSize = 10 * 1024 * 1024 // 默认10MB
	}
	
	if fileHeader.Size > maxSize {
		return false, fmt.Sprintf("文件大小不能超过%s", formatFileSize(maxSize))
	}

	// 检查文件类型
	allowedTypes, exists := AllowedFileTypes[fileCategory]
	if !exists {
		return false, "不支持的文件类别"
	}

	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if !contains(allowedTypes, ext) {
		return false, fmt.Sprintf("不支持的文件类型，支持的类型：%s", strings.Join(allowedTypes, ", "))
	}

	// 检查文件名
	if !ValidateFileName(fileHeader.Filename) {
		return false, "文件名包含非法字符"
	}

	return true, ""
}

// ValidateFileName 验证文件名
func ValidateFileName(filename string) bool {
	if filename == "" {
		return false
	}

	// 检查文件名长度
	if len(filename) > 255 {
		return false
	}

	// 检查是否包含非法字符
	illegalChars := []string{"<", ">", ":", "\"", "|", "?", "*", "\\", "/"}
	for _, char := range illegalChars {
		if strings.Contains(filename, char) {
			return false
		}
	}

	return true
}

// GenerateFileName 生成文件名
func GenerateFileName(originalName string, prefix string) string {
	ext := filepath.Ext(originalName)
	timestamp := time.Now().Format("20060102150405")
	
	// 生成随机字符串
	randomStr := generateRandomString(8)
	
	return fmt.Sprintf("%s_%s_%s%s", prefix, timestamp, randomStr, ext)
}

// GenerateFilePath 生成文件路径
func GenerateFilePath(category string, subCategory string) string {
	now := time.Now()
	year := now.Format("2006")
	month := now.Format("01")
	day := now.Format("02")
	
	if subCategory != "" {
		return fmt.Sprintf("%s/%s/%s/%s/%s", category, subCategory, year, month, day)
	}
	
	return fmt.Sprintf("%s/%s/%s/%s", category, year, month, day)
}

// CalculateFileHash 计算文件哈希
func CalculateFileHash(file multipart.File) (string, error) {
	hash := md5.New()
	
	// 重置文件指针到开始位置
	file.Seek(0, 0)
	
	_, err := io.Copy(hash, file)
	if err != nil {
		return "", err
	}
	
	// 重置文件指针到开始位置
	file.Seek(0, 0)
	
	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// GetFileType 获取文件类型
func GetFileType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	
	for category, extensions := range AllowedFileTypes {
		if contains(extensions, ext) {
			return category
		}
	}
	
	return "unknown"
}

// FormatFileSize 格式化文件大小
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	
	units := []string{"KB", "MB", "GB", "TB"}
	return fmt.Sprintf("%.1f %s", float64(size)/float64(div), units[exp])
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	// 这里应该使用真正的随机字符串生成，简化实现
	chars := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := ""
	for i := 0; i < length; i++ {
		result += string(chars[i%len(chars)])
	}
	return result
}

// IsImageFile 判断是否为图片文件
func IsImageFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	imageExts := AllowedFileTypes["image"]
	return contains(imageExts, ext)
}

// IsDocumentFile 判断是否为文档文件
func IsDocumentFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	docExts := AllowedFileTypes["document"]
	return contains(docExts, ext)
}

// GetMimeType 根据文件扩展名获取MIME类型
func GetMimeType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	
	mimeTypes := map[string]string{
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".png":  "image/png",
		".gif":  "image/gif",
		".bmp":  "image/bmp",
		".webp": "image/webp",
		".pdf":  "application/pdf",
		".doc":  "application/msword",
		".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		".txt":  "text/plain",
		".rtf":  "application/rtf",
		".zip":  "application/zip",
		".rar":  "application/x-rar-compressed",
		".7z":   "application/x-7z-compressed",
		".mp4":  "video/mp4",
		".avi":  "video/x-msvideo",
		".mov":  "video/quicktime",
		".mp3":  "audio/mpeg",
		".wav":  "audio/wav",
		".flac": "audio/flac",
	}
	
	if mimeType, exists := mimeTypes[ext]; exists {
		return mimeType
	}
	
	return "application/octet-stream"
}

// CreateFileInfo 创建文件信息
func CreateFileInfo(fileHeader *multipart.FileHeader, storedPath string, fileHash string) *FileInfo {
	return &FileInfo{
		OriginalName: fileHeader.Filename,
		FileName:     filepath.Base(storedPath),
		FileSize:     fileHeader.Size,
		FileType:     GetFileType(fileHeader.Filename),
		FilePath:     storedPath,
		FileHash:     fileHash,
		UploadTime:   time.Now().Format("2006-01-02 15:04:05"),
	}
}

// ValidateImageDimensions 验证图片尺寸（需要配合图片处理库使用）
func ValidateImageDimensions(width, height int, maxWidth, maxHeight int) (bool, string) {
	if maxWidth > 0 && width > maxWidth {
		return false, fmt.Sprintf("图片宽度不能超过%dpx", maxWidth)
	}
	
	if maxHeight > 0 && height > maxHeight {
		return false, fmt.Sprintf("图片高度不能超过%dpx", maxHeight)
	}
	
	return true, ""
}

// GetFileCategory 根据用途获取文件分类
func GetFileCategory(purpose string) string {
	categories := map[string]string{
		"complaint_evidence":    "complaint/evidence",
		"identity_certificate": "identity/certificate",
		"rights_certificate":   "rights/certificate",
		"avatar":               "user/avatar",
		"signature":            "user/signature",
	}
	
	if category, exists := categories[purpose]; exists {
		return category
	}
	
	return "misc"
}
