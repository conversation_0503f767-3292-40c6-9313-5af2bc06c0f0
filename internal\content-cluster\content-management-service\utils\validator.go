package utils

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// Validator 验证器接口
type Validator interface {
	// 基础验证
	ValidateKSUID(ksuid string) error
	ValidateContentType(contentType string) error
	ValidateStatus(status string) error
	ValidatePage(page int) error
	ValidateLimit(limit int, maxLimit int) error

	// 内容验证
	ValidateContentFilters(filters *types.ContentFilters) error
	ValidateBatchUpdateRequest(req *types.BatchUpdateStatusRequest) error
	ValidateBatchDeleteRequest(req *types.BatchDeleteRequest) error
	ValidateContentUpdateRequest(req *types.ContentUpdateRequest) error

	// 搜索验证
	ValidateSearchRequest(req *types.ContentSearchRequest) error

	// 管理验证
	ValidateOperationLogQuery(query *types.OperationLogQuery) error
	ValidateConfigQuery(query *types.ConfigQuery) error

	// 业务规则验证
	ValidateBusinessRules(operation string, data interface{}) error
}

// validator 验证器实现
type validator struct {
	maxBatchSize     int
	maxSearchResults int
	maxPageSize      int
}

// NewValidator 创建验证器
func NewValidator(maxBatchSize, maxSearchResults, maxPageSize int) Validator {
	return &validator{
		maxBatchSize:     maxBatchSize,
		maxSearchResults: maxSearchResults,
		maxPageSize:      maxPageSize,
	}
}

// KSUID正则表达式
var ksuidRegex = regexp.MustCompile(`^[0-9A-Za-z]{27}$`)

// ValidateKSUID 验证KSUID格式
func (v *validator) ValidateKSUID(ksuid string) error {
	if ksuid == "" {
		return fmt.Errorf("KSUID不能为空")
	}

	if len(ksuid) != 27 {
		return fmt.Errorf("KSUID长度必须为27个字符")
	}

	if !ksuidRegex.MatchString(ksuid) {
		return fmt.Errorf("KSUID格式无效")
	}

	return nil
}

// ValidateContentType 验证内容类型
func (v *validator) ValidateContentType(contentType string) error {
	if contentType == "" {
		return fmt.Errorf("内容类型不能为空")
	}

	validTypes := []string{"video", "novel", "music"}
	for _, validType := range validTypes {
		if contentType == validType {
			return nil
		}
	}

	return fmt.Errorf("不支持的内容类型: %s", contentType)
}

// ValidateStatus 验证状态
func (v *validator) ValidateStatus(status string) error {
	if status == "" {
		return fmt.Errorf("状态不能为空")
	}

	validStatuses := []string{"draft", "published", "archived", "deleted"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}

	return fmt.Errorf("无效的状态: %s", status)
}

// ValidatePage 验证页码
func (v *validator) ValidatePage(page int) error {
	if page < 1 {
		return fmt.Errorf("页码必须大于0")
	}
	return nil
}

// ValidateLimit 验证限制数量
func (v *validator) ValidateLimit(limit int, maxLimit int) error {
	if limit < 1 {
		return fmt.Errorf("限制数量必须大于0")
	}

	if maxLimit > 0 && limit > maxLimit {
		return fmt.Errorf("限制数量不能超过%d", maxLimit)
	}

	return nil
}

// ValidateContentFilters 验证内容过滤器
func (v *validator) ValidateContentFilters(filters *types.ContentFilters) error {
	if filters == nil {
		return nil
	}

	log.Debug().
		Interface("filters", filters).
		Msg("开始验证内容过滤器")

	// 验证内容类型
	for _, contentType := range filters.ContentTypes {
		if err := v.ValidateContentType(contentType); err != nil {
			return fmt.Errorf("内容类型验证失败: %w", err)
		}
	}

	// 验证状态
	if filters.Status != "" {
		if err := v.ValidateStatus(filters.Status); err != nil {
			return fmt.Errorf("状态验证失败: %w", err)
		}
	}

	// 验证用户KSUID
	if filters.UserKSUID != "" {
		if err := v.ValidateKSUID(filters.UserKSUID); err != nil {
			return fmt.Errorf("用户KSUID验证失败: %w", err)
		}
	}

	// 验证分页参数
	if filters.Page > 0 {
		if err := v.ValidatePage(filters.Page); err != nil {
			return fmt.Errorf("页码验证失败: %w", err)
		}
	}

	if filters.Limit > 0 {
		if err := v.ValidateLimit(filters.Limit, v.maxPageSize); err != nil {
			return fmt.Errorf("限制数量验证失败: %w", err)
		}
	}

	// 验证排序字段
	if filters.SortBy != "" {
		validSortFields := []string{"created_at", "updated_at", "published_at", "view_count", "like_count", "comment_count"}
		isValid := false
		for _, field := range validSortFields {
			if filters.SortBy == field {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("无效的排序字段: %s", filters.SortBy)
		}
	}

	// 验证排序方向
	if filters.SortOrder != "" {
		if filters.SortOrder != "asc" && filters.SortOrder != "desc" {
			return fmt.Errorf("无效的排序方向: %s", filters.SortOrder)
		}
	}

	// 验证时间范围
	if filters.CreatedAfter != nil && filters.CreatedBefore != nil {
		if filters.CreatedAfter.After(*filters.CreatedBefore) {
			return fmt.Errorf("开始时间不能晚于结束时间")
		}
	}

	log.Debug().Msg("内容过滤器验证成功")
	return nil
}

// ValidateBatchUpdateRequest 验证批量更新请求
func (v *validator) ValidateBatchUpdateRequest(req *types.BatchUpdateStatusRequest) error {
	if req == nil {
		return fmt.Errorf("批量更新请求不能为空")
	}

	log.Debug().
		Int("content_count", len(req.ContentKSUIDs)).
		Str("status", req.Status).
		Msg("开始验证批量更新请求")

	// 验证内容KSUID列表
	if len(req.ContentKSUIDs) == 0 {
		return fmt.Errorf("内容KSUID列表不能为空")
	}

	if len(req.ContentKSUIDs) > v.maxBatchSize {
		return fmt.Errorf("批量操作数量不能超过%d", v.maxBatchSize)
	}

	// 验证每个KSUID
	for i, ksuid := range req.ContentKSUIDs {
		if err := v.ValidateKSUID(ksuid); err != nil {
			return fmt.Errorf("第%d个内容KSUID验证失败: %w", i+1, err)
		}
	}

	// 验证状态
	if err := v.ValidateStatus(req.Status); err != nil {
		return fmt.Errorf("状态验证失败: %w", err)
	}

	// 验证原因长度
	if len(req.Reason) > 500 {
		return fmt.Errorf("原因描述不能超过500个字符")
	}

	log.Debug().Msg("批量更新请求验证成功")
	return nil
}

// ValidateBatchDeleteRequest 验证批量删除请求
func (v *validator) ValidateBatchDeleteRequest(req *types.BatchDeleteRequest) error {
	if req == nil {
		return fmt.Errorf("批量删除请求不能为空")
	}

	log.Debug().
		Int("content_count", len(req.ContentKSUIDs)).
		Msg("开始验证批量删除请求")

	// 验证内容KSUID列表
	if len(req.ContentKSUIDs) == 0 {
		return fmt.Errorf("内容KSUID列表不能为空")
	}

	if len(req.ContentKSUIDs) > v.maxBatchSize {
		return fmt.Errorf("批量操作数量不能超过%d", v.maxBatchSize)
	}

	// 验证每个KSUID
	for i, ksuid := range req.ContentKSUIDs {
		if err := v.ValidateKSUID(ksuid); err != nil {
			return fmt.Errorf("第%d个内容KSUID验证失败: %w", i+1, err)
		}
	}

	// 验证原因长度
	if len(req.Reason) > 500 {
		return fmt.Errorf("原因描述不能超过500个字符")
	}

	log.Debug().Msg("批量删除请求验证成功")
	return nil
}

// ValidateContentUpdateRequest 验证内容更新请求
func (v *validator) ValidateContentUpdateRequest(req *types.ContentUpdateRequest) error {
	if req == nil {
		return fmt.Errorf("内容更新请求不能为空")
	}

	log.Debug().
		Str("title", req.Title).
		Str("status", req.Status).
		Msg("开始验证内容更新请求")

	// 验证标题长度
	if req.Title != "" && len(req.Title) > 200 {
		return fmt.Errorf("标题不能超过200个字符")
	}

	// 验证描述长度
	if req.Description != "" && len(req.Description) > 2000 {
		return fmt.Errorf("描述不能超过2000个字符")
	}

	// 验证状态
	if req.Status != "" {
		if err := v.ValidateStatus(req.Status); err != nil {
			return fmt.Errorf("状态验证失败: %w", err)
		}
	}

	// 验证标签数量和长度
	if len(req.Tags) > 10 {
		return fmt.Errorf("标签数量不能超过10个")
	}

	for i, tag := range req.Tags {
		if len(tag) > 50 {
			return fmt.Errorf("第%d个标签长度不能超过50个字符", i+1)
		}
		if strings.TrimSpace(tag) == "" {
			return fmt.Errorf("第%d个标签不能为空", i+1)
		}
	}

	log.Debug().Msg("内容更新请求验证成功")
	return nil
}

// ValidateSearchRequest 验证搜索请求
func (v *validator) ValidateSearchRequest(req *types.ContentSearchRequest) error {
	if req == nil {
		return fmt.Errorf("搜索请求不能为空")
	}

	log.Debug().
		Str("query", req.Query).
		Interface("content_types", req.ContentTypes).
		Msg("开始验证搜索请求")

	// 验证查询字符串
	if strings.TrimSpace(req.Query) == "" {
		return fmt.Errorf("搜索查询不能为空")
	}

	if len(req.Query) > 200 {
		return fmt.Errorf("搜索查询不能超过200个字符")
	}

	// 验证内容类型
	for _, contentType := range req.ContentTypes {
		if err := v.ValidateContentType(contentType); err != nil {
			return fmt.Errorf("内容类型验证失败: %w", err)
		}
	}

	// 验证状态
	if req.Status != "" {
		if err := v.ValidateStatus(req.Status); err != nil {
			return fmt.Errorf("状态验证失败: %w", err)
		}
	}

	// 验证分页参数
	if req.Page > 0 {
		if err := v.ValidatePage(req.Page); err != nil {
			return fmt.Errorf("页码验证失败: %w", err)
		}
	}

	if req.Limit > 0 {
		if err := v.ValidateLimit(req.Limit, v.maxSearchResults); err != nil {
			return fmt.Errorf("限制数量验证失败: %w", err)
		}
	}

	// 验证时间范围
	if req.CreatedAfter != nil && req.CreatedBefore != nil {
		if req.CreatedAfter.After(*req.CreatedBefore) {
			return fmt.Errorf("开始时间不能晚于结束时间")
		}
	}

	log.Debug().Msg("搜索请求验证成功")
	return nil
}

// ValidateOperationLogQuery 验证操作日志查询
func (v *validator) ValidateOperationLogQuery(query *types.OperationLogQuery) error {
	if query == nil {
		return nil
	}

	log.Debug().
		Str("operator_ksuid", query.OperatorKSUID).
		Str("operation_type", query.OperationType).
		Msg("开始验证操作日志查询")

	// 验证操作者KSUID
	if query.OperatorKSUID != "" {
		if err := v.ValidateKSUID(query.OperatorKSUID); err != nil {
			return fmt.Errorf("操作者KSUID验证失败: %w", err)
		}
	}

	// 验证目标KSUID
	if query.TargetKSUID != "" {
		if err := v.ValidateKSUID(query.TargetKSUID); err != nil {
			return fmt.Errorf("目标KSUID验证失败: %w", err)
		}
	}

	// 验证操作类型
	if query.OperationType != "" {
		validTypes := []string{"create", "update", "delete", "batch_update", "batch_delete", "status_change", "view", "export", "import"}
		isValid := false
		for _, validType := range validTypes {
			if query.OperationType == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("无效的操作类型: %s", query.OperationType)
		}
	}

	// 验证目标类型
	if query.TargetType != "" {
		validTypes := []string{"content", "video", "novel", "music", "user", "category", "tag", "system"}
		isValid := false
		for _, validType := range validTypes {
			if query.TargetType == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("无效的目标类型: %s", query.TargetType)
		}
	}

	// 验证时间范围
	if query.StartTime != nil && query.EndTime != nil {
		if query.StartTime.After(*query.EndTime) {
			return fmt.Errorf("开始时间不能晚于结束时间")
		}
	}

	// 验证分页参数
	if query.Page > 0 {
		if err := v.ValidatePage(query.Page); err != nil {
			return fmt.Errorf("页码验证失败: %w", err)
		}
	}

	if query.Limit > 0 {
		if err := v.ValidateLimit(query.Limit, v.maxPageSize); err != nil {
			return fmt.Errorf("限制数量验证失败: %w", err)
		}
	}

	log.Debug().Msg("操作日志查询验证成功")
	return nil
}

// ValidateConfigQuery 验证配置查询
func (v *validator) ValidateConfigQuery(query *types.ConfigQuery) error {
	if query == nil {
		return nil
	}

	log.Debug().
		Str("category", query.Category).
		Msg("开始验证配置查询")

	// 验证分类
	if query.Category != "" {
		validCategories := []string{"limits", "cache", "security", "service", "feature", "system"}
		isValid := false
		for _, validCategory := range validCategories {
			if query.Category == validCategory {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("无效的配置分类: %s", query.Category)
		}
	}

	// 验证分页参数
	if query.Page > 0 {
		if err := v.ValidatePage(query.Page); err != nil {
			return fmt.Errorf("页码验证失败: %w", err)
		}
	}

	if query.Limit > 0 {
		if err := v.ValidateLimit(query.Limit, v.maxPageSize); err != nil {
			return fmt.Errorf("限制数量验证失败: %w", err)
		}
	}

	log.Debug().Msg("配置查询验证成功")
	return nil
}

// ValidateBusinessRules 验证业务规则
func (v *validator) ValidateBusinessRules(operation string, data interface{}) error {
	log.Debug().
		Str("operation", operation).
		Msg("开始验证业务规则")

	switch operation {
	case "batch_update_status":
		return v.validateBatchUpdateBusinessRules(data)
	case "batch_delete":
		return v.validateBatchDeleteBusinessRules(data)
	case "content_search":
		return v.validateSearchBusinessRules(data)
	default:
		log.Debug().
			Str("operation", operation).
			Msg("未定义的业务规则验证操作")
		return nil
	}
}

// validateBatchUpdateBusinessRules 验证批量更新业务规则
func (v *validator) validateBatchUpdateBusinessRules(data interface{}) error {
	req, ok := data.(*types.BatchUpdateStatusRequest)
	if !ok {
		return fmt.Errorf("无效的批量更新请求数据类型")
	}

	// 业务规则：不能将已删除的内容更新为其他状态
	if req.Status != "deleted" {
		// 这里需要查询数据库检查内容当前状态，但为了避免循环依赖，
		// 这个检查应该在service层进行
		log.Debug().Msg("批量更新业务规则检查：状态转换规则")
	}

	// 业务规则：批量操作需要提供原因
	if len(req.ContentKSUIDs) > 10 && strings.TrimSpace(req.Reason) == "" {
		return fmt.Errorf("批量操作超过10个内容时必须提供操作原因")
	}

	return nil
}

// validateBatchDeleteBusinessRules 验证批量删除业务规则
func (v *validator) validateBatchDeleteBusinessRules(data interface{}) error {
	req, ok := data.(*types.BatchDeleteRequest)
	if !ok {
		return fmt.Errorf("无效的批量删除请求数据类型")
	}

	// 业务规则：批量删除需要提供原因
	if len(req.ContentKSUIDs) > 5 && strings.TrimSpace(req.Reason) == "" {
		return fmt.Errorf("批量删除超过5个内容时必须提供删除原因")
	}

	return nil
}

// validateSearchBusinessRules 验证搜索业务规则
func (v *validator) validateSearchBusinessRules(data interface{}) error {
	req, ok := data.(*types.ContentSearchRequest)
	if !ok {
		return fmt.Errorf("无效的搜索请求数据类型")
	}

	// 业务规则：搜索查询不能包含敏感词
	sensitiveWords := []string{"test", "debug"} // 示例敏感词
	queryLower := strings.ToLower(req.Query)
	for _, word := range sensitiveWords {
		if strings.Contains(queryLower, word) {
			log.Warn().
				Str("query", req.Query).
				Str("sensitive_word", word).
				Msg("搜索查询包含敏感词")
			// 这里可以选择返回错误或者记录日志
		}
	}

	// 业务规则：限制搜索结果数量
	if req.Limit > v.maxSearchResults {
		return fmt.Errorf("搜索结果数量不能超过%d", v.maxSearchResults)
	}

	return nil
}

// ValidateTimeRange 验证时间范围
func ValidateTimeRange(startTime, endTime *time.Time) error {
	if startTime == nil || endTime == nil {
		return nil
	}

	if startTime.After(*endTime) {
		return fmt.Errorf("开始时间不能晚于结束时间")
	}

	// 验证时间范围不能超过一年
	if endTime.Sub(*startTime) > 365*24*time.Hour {
		return fmt.Errorf("时间范围不能超过一年")
	}

	return nil
}

// ValidateStringLength 验证字符串长度
func ValidateStringLength(str string, fieldName string, maxLength int) error {
	if len(str) > maxLength {
		return fmt.Errorf("%s长度不能超过%d个字符", fieldName, maxLength)
	}
	return nil
}

// ValidateStringNotEmpty 验证字符串不为空
func ValidateStringNotEmpty(str string, fieldName string) error {
	if strings.TrimSpace(str) == "" {
		return fmt.Errorf("%s不能为空", fieldName)
	}
	return nil
}

// ValidateSliceLength 验证切片长度
func ValidateSliceLength(slice []string, fieldName string, maxLength int) error {
	if len(slice) > maxLength {
		return fmt.Errorf("%s数量不能超过%d个", fieldName, maxLength)
	}
	return nil
}
