package impl

import (
	"context"
	"errors"
	"strings"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// trademarkCategoryRepo 商标类型数据访问层实现
type trademarkCategoryRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewTrademarkCategoryRepository 创建商标类型数据访问层实例
func NewTrademarkCategoryRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.TrademarkCategoryRepository {
	return &trademarkCategoryRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *trademarkCategoryRepo) GetDB() interface{} {
	return r.db
}

// Create 创建商标类型
func (r *trademarkCategoryRepo) Create(ctx context.Context, category *model.TrademarkCategory) error {
	log.Debug().
		Int("category_number", category.CategoryNumber).
		Str("name", category.Name).
		Msg("开始创建商标类型记录")

	err := r.db.WithContext(ctx).Create(category).Error
	if err != nil {
		log.Error().
			Err(err).
			Int("category_number", category.CategoryNumber).
			Msg("创建商标类型记录失败")
		return err
	}

	log.Debug().
		Int("category_number", category.CategoryNumber).
		Msg("创建商标类型记录成功")

	return nil
}

// GetByNumber 根据类别号获取商标类型
func (r *trademarkCategoryRepo) GetByNumber(ctx context.Context, categoryNumber int) (*model.TrademarkCategory, error) {
	var category model.TrademarkCategory
	err := r.db.WithContext(ctx).
		Where("category_number = ?", categoryNumber).
		First(&category).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrTrademarkCategoryNotFound
		}
		return nil, err
	}

	return &category, nil
}

// GetByID 根据ID获取商标类型
func (r *trademarkCategoryRepo) GetByID(ctx context.Context, id uint) (*model.TrademarkCategory, error) {
	var category model.TrademarkCategory
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&category).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrTrademarkCategoryNotFound
		}
		return nil, err
	}

	return &category, nil
}

// Update 更新商标类型
func (r *trademarkCategoryRepo) Update(ctx context.Context, category *model.TrademarkCategory) error {
	log.Debug().
		Int("category_number", category.CategoryNumber).
		Msg("开始更新商标类型记录")

	err := r.db.WithContext(ctx).Save(category).Error
	if err != nil {
		log.Error().
			Err(err).
			Int("category_number", category.CategoryNumber).
			Msg("更新商标类型记录失败")
		return err
	}

	log.Debug().
		Int("category_number", category.CategoryNumber).
		Msg("更新商标类型记录成功")

	return nil
}

// Delete 删除商标类型
func (r *trademarkCategoryRepo) Delete(ctx context.Context, categoryNumber int) error {
	log.Debug().
		Int("category_number", categoryNumber).
		Msg("开始删除商标类型记录")

	err := r.db.WithContext(ctx).
		Where("category_number = ?", categoryNumber).
		Delete(&model.TrademarkCategory{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("category_number", categoryNumber).
			Msg("删除商标类型记录失败")
		return err
	}

	log.Debug().
		Int("category_number", categoryNumber).
		Msg("删除商标类型记录成功")

	return nil
}

// GetAll 获取所有商标类型
func (r *trademarkCategoryRepo) GetAll(ctx context.Context) ([]*model.TrademarkCategory, error) {
	var categories []*model.TrademarkCategory
	err := r.db.WithContext(ctx).
		Order("category_number ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// GetActive 获取启用的商标类型列表
func (r *trademarkCategoryRepo) GetActive(ctx context.Context) ([]*model.TrademarkCategory, error) {
	var categories []*model.TrademarkCategory
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("category_number ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// Search 搜索商标类型
func (r *trademarkCategoryRepo) Search(ctx context.Context, keyword string) ([]*model.TrademarkCategory, error) {
	var categories []*model.TrademarkCategory
	keyword = "%" + strings.ToLower(keyword) + "%"
	
	err := r.db.WithContext(ctx).
		Where("LOWER(name) LIKE ? OR LOWER(description) LIKE ?", keyword, keyword).
		Order("category_number ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// ExistsByNumber 检查商标类别号是否存在
func (r *trademarkCategoryRepo) ExistsByNumber(ctx context.Context, categoryNumber int) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.TrademarkCategory{}).
		Where("category_number = ?", categoryNumber).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdateStatus 更新商标类型状态
func (r *trademarkCategoryRepo) UpdateStatus(ctx context.Context, categoryNumber int, isActive bool) error {
	err := r.db.WithContext(ctx).
		Model(&model.TrademarkCategory{}).
		Where("category_number = ?", categoryNumber).
		Update("is_active", isActive).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("category_number", categoryNumber).
			Bool("is_active", isActive).
			Msg("更新商标类型状态失败")
		return err
	}

	return nil
}

// GetMaxSortOrder 获取最大排序值
func (r *trademarkCategoryRepo) GetMaxSortOrder(ctx context.Context) (int, error) {
	var maxOrder int
	err := r.db.WithContext(ctx).
		Model(&model.TrademarkCategory{}).
		Select("COALESCE(MAX(sort_order), 0)").
		Scan(&maxOrder).Error
	return maxOrder, err
}
