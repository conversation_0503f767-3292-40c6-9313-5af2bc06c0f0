package model

import (
	"time"
	"gorm.io/gorm"
)

// ContentCache 内容缓存模型
type ContentCache struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	ContentKSUID  string    `gorm:"type:varchar(32);not null;uniqueIndex" json:"content_ksuid"`
	ContentType   string    `gorm:"type:varchar(20);not null;index" json:"content_type"`
	CacheData     string    `gorm:"type:jsonb;not null" json:"cache_data"`
	ExpiresAt     time.Time `gorm:"not null;index" json:"expires_at"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 指定表名
func (ContentCache) TableName() string {
	return "content_caches"
}

// BeforeCreate GORM钩子，创建前执行
func (cc *ContentCache) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if cc.CreatedAt.IsZero() {
		cc.CreatedAt = now
	}
	if cc.UpdatedAt.IsZero() {
		cc.UpdatedAt = now
	}
	return nil
}

// BeforeUpdate GORM钩子，更新前执行
func (cc *ContentCache) BeforeUpdate(tx *gorm.DB) error {
	cc.UpdatedAt = time.Now()
	return nil
}

// IsExpired 检查缓存是否过期
func (cc *ContentCache) IsExpired() bool {
	return time.Now().After(cc.ExpiresAt)
}

// SetExpiration 设置过期时间
func (cc *ContentCache) SetExpiration(duration time.Duration) {
	cc.ExpiresAt = time.Now().Add(duration)
}

// NewContentCache 创建新的内容缓存
func NewContentCache(contentKSUID, contentType, cacheData string, ttl time.Duration) *ContentCache {
	now := time.Now()
	return &ContentCache{
		ContentKSUID: contentKSUID,
		ContentType:  contentType,
		CacheData:    cacheData,
		ExpiresAt:    now.Add(ttl),
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// ContentCacheStats 内容缓存统计
type ContentCacheStats struct {
	TotalCaches     int64            `json:"total_caches"`
	CachesByType    map[string]int64 `json:"caches_by_type"`
	ExpiredCaches   int64            `json:"expired_caches"`
	CacheHitRate    float64          `json:"cache_hit_rate"`
	AvgCacheAge     float64          `json:"avg_cache_age"` // 平均缓存年龄（小时）
}

// CacheMetrics 缓存指标
type CacheMetrics struct {
	Hits        int64     `json:"hits"`
	Misses      int64     `json:"misses"`
	Evictions   int64     `json:"evictions"`
	LastUpdated time.Time `json:"last_updated"`
}

// GetHitRate 获取缓存命中率
func (cm *CacheMetrics) GetHitRate() float64 {
	total := cm.Hits + cm.Misses
	if total == 0 {
		return 0.0
	}
	return float64(cm.Hits) / float64(total) * 100
}

// IncrementHit 增加命中次数
func (cm *CacheMetrics) IncrementHit() {
	cm.Hits++
	cm.LastUpdated = time.Now()
}

// IncrementMiss 增加未命中次数
func (cm *CacheMetrics) IncrementMiss() {
	cm.Misses++
	cm.LastUpdated = time.Now()
}

// IncrementEviction 增加驱逐次数
func (cm *CacheMetrics) IncrementEviction() {
	cm.Evictions++
	cm.LastUpdated = time.Now()
}

// 内容类型常量
const (
	CacheTypeVideo = "video"
	CacheTypeNovel = "novel"
	CacheTypeMusic = "music"
	CacheTypeStats = "stats"
	CacheTypeUser  = "user"
)

// IsValidCacheType 检查缓存类型是否有效
func IsValidCacheType(cacheType string) bool {
	validTypes := []string{
		CacheTypeVideo,
		CacheTypeNovel,
		CacheTypeMusic,
		CacheTypeStats,
		CacheTypeUser,
	}
	
	for _, validType := range validTypes {
		if cacheType == validType {
			return true
		}
	}
	return false
}

// CacheKey 缓存键结构
type CacheKey struct {
	Prefix    string
	ContentID string
	Suffix    string
}

// String 生成缓存键字符串
func (ck *CacheKey) String() string {
	if ck.Suffix != "" {
		return ck.Prefix + ":" + ck.ContentID + ":" + ck.Suffix
	}
	return ck.Prefix + ":" + ck.ContentID
}

// NewCacheKey 创建新的缓存键
func NewCacheKey(prefix, contentID string) *CacheKey {
	return &CacheKey{
		Prefix:    prefix,
		ContentID: contentID,
	}
}

// WithSuffix 添加后缀
func (ck *CacheKey) WithSuffix(suffix string) *CacheKey {
	ck.Suffix = suffix
	return ck
}

// 缓存键前缀常量
const (
	CacheKeyPrefixContent     = "content"
	CacheKeyPrefixInteraction = "interaction"
	CacheKeyPrefixUserContent = "user_content"
	CacheKeyPrefixStats       = "stats"
	CacheKeyPrefixCategory    = "category"
	CacheKeyPrefixTag         = "tag"
)

// GenerateContentCacheKey 生成内容缓存键
func GenerateContentCacheKey(contentKSUID string) string {
	return NewCacheKey(CacheKeyPrefixContent, contentKSUID).String()
}

// GenerateInteractionCacheKey 生成交互统计缓存键
func GenerateInteractionCacheKey(contentKSUID string) string {
	return NewCacheKey(CacheKeyPrefixInteraction, contentKSUID).String()
}

// GenerateUserContentCacheKey 生成用户内容缓存键
func GenerateUserContentCacheKey(userKSUID string, contentType string) string {
	key := NewCacheKey(CacheKeyPrefixUserContent, userKSUID)
	if contentType != "" {
		key = key.WithSuffix(contentType)
	}
	return key.String()
}

// GenerateStatsCacheKey 生成统计缓存键
func GenerateStatsCacheKey(statsType string) string {
	return NewCacheKey(CacheKeyPrefixStats, statsType).String()
}

// CacheCleanupJob 缓存清理任务
type CacheCleanupJob struct {
	LastRun       time.Time `json:"last_run"`
	CleanedCount  int64     `json:"cleaned_count"`
	NextRun       time.Time `json:"next_run"`
	IsRunning     bool      `json:"is_running"`
}

// ShouldRun 检查是否应该运行清理任务
func (ccj *CacheCleanupJob) ShouldRun() bool {
	return !ccj.IsRunning && time.Now().After(ccj.NextRun)
}

// MarkAsRunning 标记为正在运行
func (ccj *CacheCleanupJob) MarkAsRunning() {
	ccj.IsRunning = true
	ccj.LastRun = time.Now()
}

// MarkAsCompleted 标记为完成
func (ccj *CacheCleanupJob) MarkAsCompleted(cleanedCount int64, nextRunInterval time.Duration) {
	ccj.IsRunning = false
	ccj.CleanedCount = cleanedCount
	ccj.NextRun = time.Now().Add(nextRunInterval)
}
