# 投诉服务 (Complaint Service)

投诉服务是PXPAT平台的核心服务之一，负责处理用户投诉、身份认证、权益认证等功能。

## 功能特性

### 投诉管理
- **盗版稿件投诉**: 支持原创者和路人举报盗版内容
- **稿件投诉**: 支持举报违规内容，包含多级违规分类
- **侵权申诉**: 支持权益人申诉侵权内容
- **投诉处理**: 管理员可以审核和处理投诉
- **投诉统计**: 提供投诉相关的统计数据

### 身份认证
- **自然人认证**: 支持个人身份认证
- **法人认证**: 支持企业/机构身份认证
- **多地区支持**: 支持中国大陆、香港、澳门、台湾等地区
- **认证审核**: 管理员可以审核身份认证申请

### 权益认证
- **著作权认证**: 支持视频、动漫、漫画、小说等著作权认证
- **商标权认证**: 支持45个商标类别的商标权认证
- **人格权认证**: 支持姓名权、肖像权、名誉权等人格权认证
- **代理认证**: 支持代理人代为申请权益认证

### 文件管理
- **证据文件上传**: 支持投诉证据文件上传
- **权益证明文件**: 支持权益认证相关证明文件上传
- **多格式支持**: 支持图片、PDF、Word等多种文件格式

## 技术架构

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External API  │    │   Internal API  │    │   Admin API     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                        Handler Layer                            │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                        Service Layer                            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │ ComplaintService│ │ IdentityService │ │  RightsService  │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                      Repository Layer                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │ComplaintRepo    │ │ IdentityRepo    │ │  RightsRepo     │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                        Data Layer                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │      MySQL      │ │      Redis      │ │     MinIO       │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 技术栈
- **语言**: Go 1.21+
- **框架**: Gin
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **存储**: MinIO
- **消息队列**: RabbitMQ
- **配置管理**: Viper
- **日志**: Zerolog
- **文档**: Swagger

## 目录结构

```
complaint-service/
├── client/                 # 客户端SDK
├── dto/                   # 数据传输对象
├── external/              # 外部服务接口
│   ├── handler/          # HTTP处理器
│   └── service/          # 业务服务
├── intra/                # 内部服务接口
│   ├── handler/          # 内部处理器
│   └── service/          # 内部服务
├── messaging/            # 消息处理
│   ├── consumer/         # 消息消费者
│   └── publisher/        # 消息发布者
├── middleware/           # 中间件
├── migrations/           # 数据库迁移
├── model/               # 数据模型
├── repository/          # 数据访问层
│   └── impl/           # 实现
├── routes/              # 路由配置
│   ├── complaint/      # 投诉路由
│   ├── identity/       # 身份认证路由
│   └── rights/         # 权益认证路由
├── types/               # 类型定义
└── utils/               # 工具函数
```

## API接口

### 投诉管理
- `POST /api/v1/complaints` - 创建投诉
- `GET /api/v1/complaints/{id}` - 获取投诉详情
- `PUT /api/v1/complaints/{id}` - 更新投诉
- `DELETE /api/v1/complaints/{id}` - 删除投诉
- `GET /api/v1/complaints/my` - 获取我的投诉列表
- `GET /api/v1/complaints/against-me` - 获取针对我的投诉列表
- `GET /api/v1/complaints/stats` - 获取投诉统计
- `GET /api/v1/complaints/violation-categories` - 获取违规类别

### 身份认证
- `POST /api/v1/identity/verification` - 创建身份认证
- `GET /api/v1/identity/verification/{id}` - 获取身份认证详情
- `PUT /api/v1/identity/verification/{id}` - 更新身份认证
- `GET /api/v1/identity/verification/my` - 获取我的身份认证
- `GET /api/v1/identity/countries` - 获取国家地区列表
- `GET /api/v1/identity/trademark-categories` - 获取商标类别列表

### 权益认证
- `POST /api/v1/rights/verification` - 创建权益认证
- `GET /api/v1/rights/verification/{id}` - 获取权益认证详情
- `GET /api/v1/rights/verification/my` - 获取我的权益认证列表

### 管理员接口
- `POST /api/v1/admin/complaints/{id}/process` - 处理投诉
- `POST /api/v1/admin/identity/{id}/review` - 审核身份认证
- `POST /api/v1/admin/rights/{id}/review` - 审核权益认证

## 数据模型

### 投诉相关
- `complaint_complaints` - 投诉记录表
- `complaint_evidences` - 投诉证据文件表
- `complaint_violation_categories` - 违规类别表

### 身份认证相关
- `complaint_identity_verifications` - 身份认证表
- `complaint_countries` - 国家地区表
- `complaint_trademark_categories` - 商标类别表

### 权益认证相关
- `complaint_rights_verifications` - 权益认证表
- `complaint_copyrights` - 著作权表
- `complaint_trademarks` - 商标权表
- `complaint_personality_rights` - 人格权表
- `complaint_rights_evidences` - 权益证明文件表

## 部署说明

### 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 6.0+
- MinIO (可选)

### 配置文件
复制 `config/complaint-service.yaml.example` 到 `config/complaint-service.yaml` 并修改相应配置。

### 数据库初始化
```bash
# 执行数据库迁移
mysql -u root -p pxpat_complaint < internal/user-cluster/complaint-service/migrations/001_create_complaint_tables.sql
mysql -u root -p pxpat_complaint < internal/user-cluster/complaint-service/migrations/002_create_rights_tables.sql
mysql -u root -p pxpat_complaint < internal/user-cluster/complaint-service/migrations/003_insert_basic_data.sql
```

### 启动服务
```bash
# 开发环境
go run cmd/user-cluster/complaint-service/main.go

# 生产环境
go build -o complaint-service cmd/user-cluster/complaint-service/main.go
./complaint-service
```

## 开发指南

### 添加新功能
1. 在 `model/` 目录下定义数据模型
2. 在 `dto/` 目录下定义请求/响应结构
3. 在 `repository/` 目录下实现数据访问层
4. 在 `service/` 目录下实现业务逻辑
5. 在 `handler/` 目录下实现HTTP处理器
6. 在 `routes/` 目录下配置路由

### 代码规范
- 遵循Go官方代码规范
- 使用有意义的变量和函数名
- 添加必要的注释和文档
- 编写单元测试

### 测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/user-cluster/complaint-service/service

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## 监控和日志

### 健康检查
- `GET /health` - 服务健康检查

### 指标监控
- `GET /metrics` - Prometheus指标

### 日志
服务使用结构化日志，支持多种输出格式和级别。

## 许可证

本项目采用 MIT 许可证。
