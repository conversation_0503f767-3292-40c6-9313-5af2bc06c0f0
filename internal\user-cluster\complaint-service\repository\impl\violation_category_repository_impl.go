package impl

import (
	"context"
	"errors"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// violationCategoryRepo 违规类别数据访问层实现
type violationCategoryRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewViolationCategoryRepository 创建违规类别数据访问层实例
func NewViolationCategoryRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.ViolationCategoryRepository {
	return &violationCategoryRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *violationCategoryRepo) GetDB() interface{} {
	return r.db
}

// Create 创建违规类别
func (r *violationCategoryRepo) Create(ctx context.Context, category *model.ViolationCategory) error {
	log.Debug().
		Str("category_code", category.Code).
		Str("category_name", category.Name).
		Msg("开始创建违规类别记录")

	err := r.db.WithContext(ctx).Create(category).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("category_code", category.Code).
			Msg("创建违规类别记录失败")
		return err
	}

	log.Debug().
		Str("category_code", category.Code).
		Msg("创建违规类别记录成功")

	return nil
}

// GetByCode 根据代码获取违规类别
func (r *violationCategoryRepo) GetByCode(ctx context.Context, code string) (*model.ViolationCategory, error) {
	var category model.ViolationCategory
	err := r.db.WithContext(ctx).
		Where("code = ?", code).
		First(&category).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrViolationCategoryNotFound
		}
		return nil, err
	}

	return &category, nil
}

// GetByID 根据ID获取违规类别
func (r *violationCategoryRepo) GetByID(ctx context.Context, id uint) (*model.ViolationCategory, error) {
	var category model.ViolationCategory
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&category).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrViolationCategoryNotFound
		}
		return nil, err
	}

	return &category, nil
}

// Update 更新违规类别
func (r *violationCategoryRepo) Update(ctx context.Context, category *model.ViolationCategory) error {
	log.Debug().
		Str("category_code", category.Code).
		Msg("开始更新违规类别记录")

	err := r.db.WithContext(ctx).Save(category).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("category_code", category.Code).
			Msg("更新违规类别记录失败")
		return err
	}

	log.Debug().
		Str("category_code", category.Code).
		Msg("更新违规类别记录成功")

	return nil
}

// Delete 删除违规类别
func (r *violationCategoryRepo) Delete(ctx context.Context, code string) error {
	log.Debug().
		Str("category_code", code).
		Msg("开始删除违规类别记录")

	err := r.db.WithContext(ctx).
		Where("code = ?", code).
		Delete(&model.ViolationCategory{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("category_code", code).
			Msg("删除违规类别记录失败")
		return err
	}

	log.Debug().
		Str("category_code", code).
		Msg("删除违规类别记录成功")

	return nil
}

// GetAll 获取所有违规类别
func (r *violationCategoryRepo) GetAll(ctx context.Context) ([]*model.ViolationCategory, error) {
	var categories []*model.ViolationCategory
	err := r.db.WithContext(ctx).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// GetByParentCode 根据父类别代码获取子类别列表
func (r *violationCategoryRepo) GetByParentCode(ctx context.Context, parentCode string) ([]*model.ViolationCategory, error) {
	var categories []*model.ViolationCategory
	err := r.db.WithContext(ctx).
		Where("parent_code = ?", parentCode).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// GetRootCategories 获取根类别列表
func (r *violationCategoryRepo) GetRootCategories(ctx context.Context) ([]*model.ViolationCategory, error) {
	var categories []*model.ViolationCategory
	err := r.db.WithContext(ctx).
		Where("parent_code IS NULL OR parent_code = ''").
		Order("sort_order ASC, name ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// GetActiveCategories 获取启用的违规类别列表
func (r *violationCategoryRepo) GetActiveCategories(ctx context.Context) ([]*model.ViolationCategory, error) {
	var categories []*model.ViolationCategory
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("sort_order ASC, name ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// GetCategoryTree 获取违规类别树
func (r *violationCategoryRepo) GetCategoryTree(ctx context.Context) ([]*model.ViolationCategory, error) {
	// 获取所有启用的类别
	var allCategories []*model.ViolationCategory
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("sort_order ASC, name ASC").
		Find(&allCategories).Error

	if err != nil {
		return nil, err
	}

	// 构建树形结构
	categoryMap := make(map[string]*model.ViolationCategory)
	var rootCategories []*model.ViolationCategory

	// 第一遍：创建映射
	for _, category := range allCategories {
		categoryMap[category.Code] = category
		category.Children = []*model.ViolationCategory{}
	}

	// 第二遍：构建父子关系
	for _, category := range allCategories {
		if category.ParentCode == "" {
			rootCategories = append(rootCategories, category)
		} else {
			if parent, exists := categoryMap[category.ParentCode]; exists {
				parent.Children = append(parent.Children, category)
			}
		}
	}

	return rootCategories, nil
}

// ExistsByCode 检查违规类别代码是否存在
func (r *violationCategoryRepo) ExistsByCode(ctx context.Context, code string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ViolationCategory{}).
		Where("code = ?", code).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdateStatus 更新违规类别状态
func (r *violationCategoryRepo) UpdateStatus(ctx context.Context, code string, isActive bool) error {
	err := r.db.WithContext(ctx).
		Model(&model.ViolationCategory{}).
		Where("code = ?", code).
		Update("is_active", isActive).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("category_code", code).
			Bool("is_active", isActive).
			Msg("更新违规类别状态失败")
		return err
	}

	return nil
}

// GetMaxSortOrder 获取最大排序值
func (r *violationCategoryRepo) GetMaxSortOrder(ctx context.Context, parentCode string) (int, error) {
	var maxOrder int
	query := r.db.WithContext(ctx).Model(&model.ViolationCategory{})
	
	if parentCode == "" {
		query = query.Where("parent_code IS NULL OR parent_code = ''")
	} else {
		query = query.Where("parent_code = ?", parentCode)
	}
	
	err := query.Select("COALESCE(MAX(sort_order), 0)").Scan(&maxOrder).Error
	return maxOrder, err
}
