-- 权益认证相关表结构

-- 1. 权益认证表
CREATE TABLE IF NOT EXISTS complaint_rights_verifications (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    -- 基础字段
    rights_ksuid VARCHAR(27) NOT NULL UNIQUE COMMENT '权益认证唯一标识',
    user_ksuid VARCHAR(27) NOT NULL COMMENT '用户KSUID',
    type ENUM('copyright', 'trademark', 'reputation', 'personality', 'nickname') NOT NULL COMMENT '权益类型',
    status ENUM('pending', 'approved', 'rejected', 'expired') DEFAULT 'pending' COMMENT '认证状态',
    
    -- 代理信息
    is_agent BOOLEAN DEFAULT FALSE COMMENT '是否代理',
    agent_type ENUM('agent', 'right_owner') COMMENT '代理类型',
    right_owner_name VARCHAR(100) COMMENT '权利人/代理人姓名',
    authorization_start_at TIMESTAMP NULL COMMENT '授权期限起始时间',
    authorization_end_at TIMESTAMP NULL COMMENT '授权期限结束时间',
    
    -- 审核信息
    reviewer_ksuid VARCHAR(27) COMMENT '审核人KSUID',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_note TEXT COMMENT '审核备注',
    reject_reason TEXT COMMENT '拒绝原因',
    
    INDEX idx_rights_ksuid (rights_ksuid),
    INDEX idx_user_ksuid (user_ksuid),
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权益认证表';

-- 2. 著作权表
CREATE TABLE IF NOT EXISTS complaint_copyrights (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    rights_ksuid VARCHAR(27) NOT NULL COMMENT '关联的权益认证KSUID',
    copyright_type ENUM('video', 'anime', 'comic', 'novel') NOT NULL COMMENT '著作类型',
    work_name VARCHAR(255) NOT NULL COMMENT '著作名称',
    country_code VARCHAR(10) NOT NULL COMMENT '地区代码',
    valid_from TIMESTAMP NULL COMMENT '期限起始时间',
    valid_to TIMESTAMP NULL COMMENT '期限结束时间',
    
    INDEX idx_rights_ksuid (rights_ksuid),
    INDEX idx_copyright_type (copyright_type),
    INDEX idx_country_code (country_code),
    INDEX idx_deleted_at (deleted_at),
    FOREIGN KEY (rights_ksuid) REFERENCES complaint_rights_verifications(rights_ksuid) ON DELETE CASCADE,
    FOREIGN KEY (country_code) REFERENCES complaint_countries(code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='著作权表';

-- 3. 商标权表
CREATE TABLE IF NOT EXISTS complaint_trademarks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    rights_ksuid VARCHAR(27) NOT NULL COMMENT '关联的权益认证KSUID',
    trademark_name VARCHAR(255) NOT NULL COMMENT '商标名称',
    category_number INT NOT NULL COMMENT '商标类别号',
    country_code VARCHAR(10) NOT NULL COMMENT '地区代码',
    valid_from TIMESTAMP NULL COMMENT '期限起始时间',
    valid_to TIMESTAMP NULL COMMENT '期限结束时间',
    
    INDEX idx_rights_ksuid (rights_ksuid),
    INDEX idx_category_number (category_number),
    INDEX idx_country_code (country_code),
    INDEX idx_deleted_at (deleted_at),
    FOREIGN KEY (rights_ksuid) REFERENCES complaint_rights_verifications(rights_ksuid) ON DELETE CASCADE,
    FOREIGN KEY (country_code) REFERENCES complaint_countries(code),
    FOREIGN KEY (category_number) REFERENCES complaint_trademark_categories(category_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商标权表';

-- 4. 人格权表
CREATE TABLE IF NOT EXISTS complaint_personality_rights (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    rights_ksuid VARCHAR(27) NOT NULL COMMENT '关联的权益认证KSUID',
    personality_rights_type ENUM('name', 'portrait', 'honor', 'privacy', 'other') NOT NULL COMMENT '人格权类型',
    
    INDEX idx_rights_ksuid (rights_ksuid),
    INDEX idx_personality_rights_type (personality_rights_type),
    INDEX idx_deleted_at (deleted_at),
    FOREIGN KEY (rights_ksuid) REFERENCES complaint_rights_verifications(rights_ksuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人格权表';

-- 5. 权益证明文件表
CREATE TABLE IF NOT EXISTS complaint_rights_evidences (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    rights_ksuid VARCHAR(27) NOT NULL COMMENT '关联的权益认证KSUID',
    related_ksuid VARCHAR(27) COMMENT '关联的具体权益KSUID(如著作权、商标权等)',
    evidence_type VARCHAR(50) NOT NULL COMMENT '证明类型(authorization, copyright, trademark, personality, nickname)',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_url VARCHAR(1024) NOT NULL COMMENT '文件URL',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小',
    content_type VARCHAR(100) COMMENT '文件类型',
    bucket_name VARCHAR(100) COMMENT '存储桶名称',
    object_name VARCHAR(512) COMMENT '对象名称',
    
    INDEX idx_rights_ksuid (rights_ksuid),
    INDEX idx_related_ksuid (related_ksuid),
    INDEX idx_evidence_type (evidence_type),
    INDEX idx_deleted_at (deleted_at),
    FOREIGN KEY (rights_ksuid) REFERENCES complaint_rights_verifications(rights_ksuid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权益证明文件表';
