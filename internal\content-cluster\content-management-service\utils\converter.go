package utils

import (
	"fmt"
	"strings"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// ContentConverter 内容转换器接口
type ContentConverter interface {
	// 视频转换
	ConvertVideoToBase(video *client.VideoContent) *types.BaseContent
	ConvertVideoToBaseWithInteraction(video *client.VideoContent, interaction *types.InteractionStats) *types.BaseContent
	ConvertVideoToDetails(video *client.VideoContent) *types.ContentWithDetails

	// 批量转换
	BatchConvertVideosToBase(videos []*client.VideoContent) []*types.BaseContent
	BatchConvertVideosToBaseWithInteraction(videos []*client.VideoContent, interactions map[string]*types.InteractionStats) []*types.BaseContent

	// 通用转换
	ConvertToBaseContent(contentType string, data interface{}) (*types.BaseContent, error)
	ConvertToContentWithDetails(contentType string, data interface{}) (*types.ContentWithDetails, error)
}

// contentConverter 内容转换器实现
type contentConverter struct{}

// NewContentConverter 创建内容转换器
func NewContentConverter() ContentConverter {
	return &contentConverter{}
}

// ConvertVideoToBase 将视频内容转换为基础内容
func (c *contentConverter) ConvertVideoToBase(video *client.VideoContent) *types.BaseContent {
	if video == nil {
		return nil
	}

	log.Debug().
		Str("content_ksuid", video.ContentKSUID).
		Msg("开始转换视频内容为基础内容")

	// 转换标签
	tags := make([]string, len(video.Tags))
	for i, tag := range video.Tags {
		tags[i] = tag.Name
	}

	baseContent := &types.BaseContent{
		ContentKSUID:    video.ContentKSUID,
		ContentType:     "video",
		Title:          video.Title,
		Description:    video.Description,
		UserKSUID:      video.UserKSUID,
		Status:         video.Status,
		ViewCount:      video.ViewCount,
		LikeCount:      0, // 需要从interaction-service获取
		CommentCount:   video.CommentCount,
		FavoriteCount:  0, // 需要从interaction-service获取
		CategoryID:     video.CategoryID,
		CategoryName:   video.Category.Name,
		Tags:           tags,
		CreatedAt:      video.CreatedAt,
		UpdatedAt:      video.UpdatedAt,
		PublishedAt:    video.PublishedAt,
		SourceService:  "video-service",
	}

	log.Debug().
		Str("content_ksuid", video.ContentKSUID).
		Str("title", baseContent.Title).
		Msg("视频内容转换为基础内容成功")

	return baseContent
}

// ConvertVideoToBaseWithInteraction 将视频内容和交互统计转换为基础内容
func (c *contentConverter) ConvertVideoToBaseWithInteraction(video *client.VideoContent, interaction *types.InteractionStats) *types.BaseContent {
	baseContent := c.ConvertVideoToBase(video)
	if baseContent == nil {
		return nil
	}

	// 添加交互统计
	if interaction != nil {
		baseContent.LikeCount = interaction.LikeCount
		baseContent.FavoriteCount = interaction.FavoriteCount
	}

	log.Debug().
		Str("content_ksuid", video.ContentKSUID).
		Int64("like_count", baseContent.LikeCount).
		Int64("favorite_count", baseContent.FavoriteCount).
		Msg("视频内容转换为带交互统计的基础内容成功")

	return baseContent
}

// ConvertVideoToDetails 将视频内容转换为详细内容
func (c *contentConverter) ConvertVideoToDetails(video *client.VideoContent) *types.ContentWithDetails {
	baseContent := c.ConvertVideoToBase(video)
	if baseContent == nil {
		return nil
	}

	// 创建视频详细信息
	videoDetails := &types.VideoDetails{
		Duration:     video.Duration,
		Resolution:   video.Resolution,
		FileSize:     video.FileSize,
		Format:       video.Format,
		Orientation:  video.Orientation,
		Language:     video.Language,
		PlayURL:      video.PlayURL,
		CoverURL:     video.CoverURL,
		PreviewURL:   video.PreviewURL,
		KeyFramesURL: video.KeyFramesURL,
		VideoID:      video.VideoID,
		Director:     video.Director,
		Actors:       video.Actors,
		AuditTaskID:  video.AuditTaskID,
		Level:        video.Level,
	}

	contentWithDetails := &types.ContentWithDetails{
		BaseContent: *baseContent,
		Details:     videoDetails,
	}

	log.Debug().
		Str("content_ksuid", video.ContentKSUID).
		Float64("duration", video.Duration).
		Msg("视频内容转换为详细内容成功")

	return contentWithDetails
}

// BatchConvertVideosToBase 批量转换视频为基础内容
func (c *contentConverter) BatchConvertVideosToBase(videos []*client.VideoContent) []*types.BaseContent {
	if len(videos) == 0 {
		return []*types.BaseContent{}
	}

	log.Debug().
		Int("count", len(videos)).
		Msg("开始批量转换视频为基础内容")

	baseContents := make([]*types.BaseContent, 0, len(videos))
	for _, video := range videos {
		if baseContent := c.ConvertVideoToBase(video); baseContent != nil {
			baseContents = append(baseContents, baseContent)
		}
	}

	log.Debug().
		Int("input_count", len(videos)).
		Int("output_count", len(baseContents)).
		Msg("批量转换视频为基础内容完成")

	return baseContents
}

// BatchConvertVideosToBaseWithInteraction 批量转换视频为带交互统计的基础内容
func (c *contentConverter) BatchConvertVideosToBaseWithInteraction(videos []*client.VideoContent, interactions map[string]*types.InteractionStats) []*types.BaseContent {
	if len(videos) == 0 {
		return []*types.BaseContent{}
	}

	log.Debug().
		Int("video_count", len(videos)).
		Int("interaction_count", len(interactions)).
		Msg("开始批量转换视频为带交互统计的基础内容")

	baseContents := make([]*types.BaseContent, 0, len(videos))
	for _, video := range videos {
		var interaction *types.InteractionStats
		if interactions != nil {
			interaction = interactions[video.ContentKSUID]
		}
		
		if baseContent := c.ConvertVideoToBaseWithInteraction(video, interaction); baseContent != nil {
			baseContents = append(baseContents, baseContent)
		}
	}

	log.Debug().
		Int("input_count", len(videos)).
		Int("output_count", len(baseContents)).
		Msg("批量转换视频为带交互统计的基础内容完成")

	return baseContents
}

// ConvertToBaseContent 通用转换为基础内容
func (c *contentConverter) ConvertToBaseContent(contentType string, data interface{}) (*types.BaseContent, error) {
	log.Debug().
		Str("content_type", contentType).
		Msg("开始通用转换为基础内容")

	switch strings.ToLower(contentType) {
	case "video":
		if video, ok := data.(*client.VideoContent); ok {
			return c.ConvertVideoToBase(video), nil
		}
		return nil, fmt.Errorf("invalid video content data type")
	
	case "novel":
		// TODO: 实现小说转换
		log.Warn().Msg("小说内容转换尚未实现")
		return nil, fmt.Errorf("novel content conversion not implemented yet")
	
	case "music":
		// TODO: 实现音乐转换
		log.Warn().Msg("音乐内容转换尚未实现")
		return nil, fmt.Errorf("music content conversion not implemented yet")
	
	default:
		return nil, fmt.Errorf("unsupported content type: %s", contentType)
	}
}

// ConvertToContentWithDetails 通用转换为详细内容
func (c *contentConverter) ConvertToContentWithDetails(contentType string, data interface{}) (*types.ContentWithDetails, error) {
	log.Debug().
		Str("content_type", contentType).
		Msg("开始通用转换为详细内容")

	switch strings.ToLower(contentType) {
	case "video":
		if video, ok := data.(*client.VideoContent); ok {
			return c.ConvertVideoToDetails(video), nil
		}
		return nil, fmt.Errorf("invalid video content data type")
	
	case "novel":
		// TODO: 实现小说转换
		log.Warn().Msg("小说详细内容转换尚未实现")
		return nil, fmt.Errorf("novel content details conversion not implemented yet")
	
	case "music":
		// TODO: 实现音乐转换
		log.Warn().Msg("音乐详细内容转换尚未实现")
		return nil, fmt.Errorf("music content details conversion not implemented yet")
	
	default:
		return nil, fmt.Errorf("unsupported content type: %s", contentType)
	}
}

// ConvertVideoListToBaseList 转换视频列表为基础内容列表
func ConvertVideoListToBaseList(videoList *client.VideoContentList, interactions map[string]*types.InteractionStats) *types.BaseContentList {
	if videoList == nil {
		return &types.BaseContentList{
			Contents: []*types.BaseContent{},
		}
	}

	log.Debug().
		Int("video_count", len(videoList.Contents)).
		Int("interaction_count", len(interactions)).
		Msg("开始转换视频列表为基础内容列表")

	converter := NewContentConverter()
	baseContents := converter.BatchConvertVideosToBaseWithInteraction(videoList.Contents, interactions)

	result := &types.BaseContentList{
		Contents:   baseContents,
		Total:      videoList.Total,
		Page:       videoList.Page,
		Limit:      videoList.Limit,
		TotalPages: videoList.TotalPages,
	}

	log.Debug().
		Int("input_count", len(videoList.Contents)).
		Int("output_count", len(baseContents)).
		Msg("转换视频列表为基础内容列表完成")

	return result
}

// ConvertVideoListToDetailsListWithInteraction 转换视频列表为详细内容列表（带交互统计）
func ConvertVideoListToDetailsListWithInteraction(videoList *client.VideoContentList, interactions map[string]*types.InteractionStats) *types.ContentWithDetailsList {
	if videoList == nil {
		return &types.ContentWithDetailsList{
			Contents:    []*types.ContentWithDetails{},
			ContentType: "video",
		}
	}

	log.Debug().
		Int("video_count", len(videoList.Contents)).
		Int("interaction_count", len(interactions)).
		Msg("开始转换视频列表为详细内容列表")

	converter := NewContentConverter()
	detailsContents := make([]*types.ContentWithDetails, 0, len(videoList.Contents))

	for _, video := range videoList.Contents {
		if detailsContent := converter.ConvertVideoToDetails(video); detailsContent != nil {
			// 添加交互统计
			if interactions != nil {
				if interaction := interactions[video.ContentKSUID]; interaction != nil {
					detailsContent.BaseContent.LikeCount = interaction.LikeCount
					detailsContent.BaseContent.FavoriteCount = interaction.FavoriteCount
				}
			}
			detailsContents = append(detailsContents, detailsContent)
		}
	}

	result := &types.ContentWithDetailsList{
		Contents:    detailsContents,
		Total:       videoList.Total,
		Page:        videoList.Page,
		Limit:       videoList.Limit,
		TotalPages:  videoList.TotalPages,
		ContentType: "video",
	}

	log.Debug().
		Int("input_count", len(videoList.Contents)).
		Int("output_count", len(detailsContents)).
		Msg("转换视频列表为详细内容列表完成")

	return result
}
