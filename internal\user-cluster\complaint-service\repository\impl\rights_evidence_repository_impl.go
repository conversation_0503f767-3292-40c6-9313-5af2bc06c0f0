package impl

import (
	"context"
	"errors"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// rightsEvidenceRepo 权益证明文件数据访问层实现
type rightsEvidenceRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewRightsEvidenceRepository 创建权益证明文件数据访问层实例
func NewRightsEvidenceRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.RightsEvidenceRepository {
	return &rightsEvidenceRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *rightsEvidenceRepo) GetDB() interface{} {
	return r.db
}

// Create 创建权益证明文件记录
func (r *rightsEvidenceRepo) Create(ctx context.Context, evidence *model.RightsEvidence) error {
	log.Debug().
		Str("rights_ksuid", evidence.RightsKSUID).
		Str("file_name", evidence.FileName).
		Msg("开始创建权益证明文件记录")

	err := r.db.WithContext(ctx).Create(evidence).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("rights_ksuid", evidence.RightsKSUID).
			Str("file_name", evidence.FileName).
			Msg("创建权益证明文件记录失败")
		return err
	}

	log.Debug().
		Str("rights_ksuid", evidence.RightsKSUID).
		Str("file_name", evidence.FileName).
		Msg("创建权益证明文件记录成功")

	return nil
}

// GetByID 根据ID获取权益证明文件
func (r *rightsEvidenceRepo) GetByID(ctx context.Context, id uint) (*model.RightsEvidence, error) {
	var evidence model.RightsEvidence
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&evidence).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrRightsEvidenceNotFound
		}
		return nil, err
	}

	return &evidence, nil
}

// Update 更新权益证明文件记录
func (r *rightsEvidenceRepo) Update(ctx context.Context, evidence *model.RightsEvidence) error {
	log.Debug().
		Uint("evidence_id", evidence.ID).
		Str("rights_ksuid", evidence.RightsKSUID).
		Msg("开始更新权益证明文件记录")

	err := r.db.WithContext(ctx).Save(evidence).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint("evidence_id", evidence.ID).
			Msg("更新权益证明文件记录失败")
		return err
	}

	log.Debug().
		Uint("evidence_id", evidence.ID).
		Msg("更新权益证明文件记录成功")

	return nil
}

// Delete 删除权益证明文件记录
func (r *rightsEvidenceRepo) Delete(ctx context.Context, id uint) error {
	log.Debug().
		Uint("evidence_id", id).
		Msg("开始删除权益证明文件记录")

	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		Delete(&model.RightsEvidence{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Uint("evidence_id", id).
			Msg("删除权益证明文件记录失败")
		return err
	}

	log.Debug().
		Uint("evidence_id", id).
		Msg("删除权益证明文件记录成功")

	return nil
}

// GetByRightsKSUID 根据权益认证KSUID获取证明文件列表
func (r *rightsEvidenceRepo) GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.RightsEvidence, error) {
	var evidences []*model.RightsEvidence
	err := r.db.WithContext(ctx).
		Where("rights_ksuid = ?", rightsKSUID).
		Order("created_at ASC").
		Find(&evidences).Error

	if err != nil {
		return nil, err
	}

	return evidences, nil
}

// GetByRelatedKSUID 根据关联KSUID获取证明文件列表
func (r *rightsEvidenceRepo) GetByRelatedKSUID(ctx context.Context, relatedKSUID string) ([]*model.RightsEvidence, error) {
	var evidences []*model.RightsEvidence
	err := r.db.WithContext(ctx).
		Where("related_ksuid = ?", relatedKSUID).
		Order("created_at ASC").
		Find(&evidences).Error

	if err != nil {
		return nil, err
	}

	return evidences, nil
}

// GetByEvidenceType 根据证明类型获取证明文件列表
func (r *rightsEvidenceRepo) GetByEvidenceType(ctx context.Context, evidenceType string) ([]*model.RightsEvidence, error) {
	var evidences []*model.RightsEvidence
	err := r.db.WithContext(ctx).
		Where("evidence_type = ?", evidenceType).
		Order("created_at DESC").
		Find(&evidences).Error

	if err != nil {
		return nil, err
	}

	return evidences, nil
}

// BatchCreate 批量创建权益证明文件记录
func (r *rightsEvidenceRepo) BatchCreate(ctx context.Context, evidences []*model.RightsEvidence) error {
	if len(evidences) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(evidences)).
		Msg("开始批量创建权益证明文件记录")

	err := r.db.WithContext(ctx).CreateInBatches(evidences, 100).Error
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(evidences)).
			Msg("批量创建权益证明文件记录失败")
		return err
	}

	log.Debug().
		Int("count", len(evidences)).
		Msg("批量创建权益证明文件记录成功")

	return nil
}

// BatchDelete 批量删除权益证明文件记录
func (r *rightsEvidenceRepo) BatchDelete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(ids)).
		Msg("开始批量删除权益证明文件记录")

	err := r.db.WithContext(ctx).
		Where("id IN ?", ids).
		Delete(&model.RightsEvidence{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(ids)).
			Msg("批量删除权益证明文件记录失败")
		return err
	}

	log.Debug().
		Int("count", len(ids)).
		Msg("批量删除权益证明文件记录成功")

	return nil
}

// DeleteByRightsKSUID 根据权益认证KSUID删除所有证明文件
func (r *rightsEvidenceRepo) DeleteByRightsKSUID(ctx context.Context, rightsKSUID string) error {
	log.Debug().
		Str("rights_ksuid", rightsKSUID).
		Msg("开始删除权益认证的所有证明文件记录")

	err := r.db.WithContext(ctx).
		Where("rights_ksuid = ?", rightsKSUID).
		Delete(&model.RightsEvidence{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("rights_ksuid", rightsKSUID).
			Msg("删除权益认证的所有证明文件记录失败")
		return err
	}

	log.Debug().
		Str("rights_ksuid", rightsKSUID).
		Msg("删除权益认证的所有证明文件记录成功")

	return nil
}

// DeleteByRelatedKSUID 根据关联KSUID删除证明文件
func (r *rightsEvidenceRepo) DeleteByRelatedKSUID(ctx context.Context, relatedKSUID string) error {
	log.Debug().
		Str("related_ksuid", relatedKSUID).
		Msg("开始删除关联的证明文件记录")

	err := r.db.WithContext(ctx).
		Where("related_ksuid = ?", relatedKSUID).
		Delete(&model.RightsEvidence{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("related_ksuid", relatedKSUID).
			Msg("删除关联的证明文件记录失败")
		return err
	}

	log.Debug().
		Str("related_ksuid", relatedKSUID).
		Msg("删除关联的证明文件记录成功")

	return nil
}

// CountByRightsKSUID 统计权益认证的证明文件数量
func (r *rightsEvidenceRepo) CountByRightsKSUID(ctx context.Context, rightsKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.RightsEvidence{}).
		Where("rights_ksuid = ?", rightsKSUID).
		Count(&count).Error
	return count, err
}

// GetTotalSizeByRightsKSUID 获取权益认证的证明文件总大小
func (r *rightsEvidenceRepo) GetTotalSizeByRightsKSUID(ctx context.Context, rightsKSUID string) (int64, error) {
	var totalSize int64
	err := r.db.WithContext(ctx).
		Model(&model.RightsEvidence{}).
		Where("rights_ksuid = ?", rightsKSUID).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&totalSize).Error
	return totalSize, err
}
