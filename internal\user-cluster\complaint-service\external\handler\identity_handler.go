package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	"pxpat-backend/pkg/response"
)

// IdentityHandler 身份认证处理器
type IdentityHandler struct {
	identityService *service.IdentityService
}

// NewIdentityHandler 创建身份认证处理器实例
func NewIdentityHandler(identityService *service.IdentityService) *IdentityHandler {
	return &IdentityHandler{
		identityService: identityService,
	}
}

// CreateIdentityVerification 创建身份认证
// @Summary 创建身份认证
// @Description 用户提交身份认证申请
// @Tags 身份认证
// @Accept json
// @Produce json
// @Param request body dto.CreateIdentityVerificationRequest true "创建身份认证请求"
// @Success 200 {object} response.Response{data=dto.IdentityVerificationResponse} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/verification [post]
func (h *IdentityHandler) CreateIdentityVerification(c *gin.Context) {
	var req dto.CreateIdentityVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("绑定创建身份认证请求参数失败")
		response.Error(c, http.StatusBadRequest, "参数格式错误", nil)
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未登录", nil)
		return
	}

	result, gErr := h.identityService.CreateIdentityVerification(c.Request.Context(), userKSUID.(string), &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("创建身份认证失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "创建身份认证成功", result)
}

// GetIdentityVerification 获取身份认证详情
// @Summary 获取身份认证详情
// @Description 获取指定身份认证的详细信息
// @Tags 身份认证
// @Accept json
// @Produce json
// @Param verification_ksuid path string true "身份认证KSUID"
// @Success 200 {object} response.Response{data=dto.IdentityVerificationResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "身份认证不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/verification/{verification_ksuid} [get]
func (h *IdentityHandler) GetIdentityVerification(c *gin.Context) {
	verificationKSUID := c.Param("verification_ksuid")
	if verificationKSUID == "" {
		response.Error(c, http.StatusBadRequest, "身份认证KSUID不能为空", nil)
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未登录", nil)
		return
	}

	result, gErr := h.identityService.GetIdentityVerification(c.Request.Context(), userKSUID.(string), verificationKSUID)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Str("verification_ksuid", verificationKSUID).
			Msg("获取身份认证详情失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "获取身份认证详情成功", result)
}

// GetMyIdentityVerification 获取我的身份认证
// @Summary 获取我的身份认证
// @Description 获取当前用户的身份认证信息
// @Tags 身份认证
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.IdentityVerificationResponse} "获取成功"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "身份认证不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/verification/my [get]
func (h *IdentityHandler) GetMyIdentityVerification(c *gin.Context) {
	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未登录", nil)
		return
	}

	result, gErr := h.identityService.GetMyIdentityVerification(c.Request.Context(), userKSUID.(string))
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Msg("获取我的身份认证失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "获取我的身份认证成功", result)
}

// UpdateIdentityVerification 更新身份认证
// @Summary 更新身份认证
// @Description 更新身份认证信息
// @Tags 身份认证
// @Accept json
// @Produce json
// @Param verification_ksuid path string true "身份认证KSUID"
// @Param request body dto.UpdateIdentityVerificationRequest true "更新身份认证请求"
// @Success 200 {object} response.Response{data=dto.IdentityVerificationResponse} "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "身份认证不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/verification/{verification_ksuid} [put]
func (h *IdentityHandler) UpdateIdentityVerification(c *gin.Context) {
	verificationKSUID := c.Param("verification_ksuid")
	if verificationKSUID == "" {
		response.Error(c, http.StatusBadRequest, "身份认证KSUID不能为空", nil)
		return
	}

	var req dto.UpdateIdentityVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("绑定更新身份认证请求参数失败")
		response.Error(c, http.StatusBadRequest, "参数格式错误", nil)
		return
	}

	// 从上下文获取用户KSUID
	userKSUID, exists := c.Get("user_ksuid")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未登录", nil)
		return
	}

	result, gErr := h.identityService.UpdateIdentityVerification(c.Request.Context(), userKSUID.(string), verificationKSUID, &req)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("user_ksuid", userKSUID.(string)).
			Str("verification_ksuid", verificationKSUID).
			Msg("更新身份认证失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "更新身份认证成功", result)
}

// GetCountries 获取国家地区列表
// @Summary 获取国家地区列表
// @Description 获取所有可用的国家地区列表
// @Tags 身份认证
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.CountryResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/countries [get]
func (h *IdentityHandler) GetCountries(c *gin.Context) {
	result, gErr := h.identityService.GetCountries(c.Request.Context())
	if gErr != nil {
		log.Error().
			Err(gErr).
			Msg("获取国家地区列表失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "获取国家地区列表成功", result)
}

// GetChinaRegions 获取中国特别行政区
// @Summary 获取中国特别行政区
// @Description 获取中国大陆、香港、澳门、台湾地区列表
// @Tags 身份认证
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.CountryResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/china-regions [get]
func (h *IdentityHandler) GetChinaRegions(c *gin.Context) {
	result, gErr := h.identityService.GetChinaRegions(c.Request.Context())
	if gErr != nil {
		log.Error().
			Err(gErr).
			Msg("获取中国特别行政区列表失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "获取中国特别行政区列表成功", result)
}

// GetTrademarkCategories 获取商标类型列表
// @Summary 获取商标类型列表
// @Description 获取所有可用的商标类型列表
// @Tags 身份认证
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.TrademarkCategoryResponse} "获取成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/trademark-categories [get]
func (h *IdentityHandler) GetTrademarkCategories(c *gin.Context) {
	result, gErr := h.identityService.GetTrademarkCategories(c.Request.Context())
	if gErr != nil {
		log.Error().
			Err(gErr).
			Msg("获取商标类型列表失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "获取商标类型列表成功", result)
}

// SearchCountries 搜索国家地区
// @Summary 搜索国家地区
// @Description 根据关键词搜索国家地区
// @Tags 身份认证
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Success 200 {object} response.Response{data=[]dto.CountryResponse} "搜索成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/countries/search [get]
func (h *IdentityHandler) SearchCountries(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		response.Error(c, http.StatusBadRequest, "搜索关键词不能为空", nil)
		return
	}

	result, gErr := h.identityService.SearchCountries(c.Request.Context(), keyword)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("keyword", keyword).
			Msg("搜索国家地区失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "搜索国家地区成功", result)
}

// SearchTrademarkCategories 搜索商标类型
// @Summary 搜索商标类型
// @Description 根据关键词搜索商标类型
// @Tags 身份认证
// @Accept json
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Success 200 {object} response.Response{data=[]dto.TrademarkCategoryResponse} "搜索成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/identity/trademark-categories/search [get]
func (h *IdentityHandler) SearchTrademarkCategories(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		response.Error(c, http.StatusBadRequest, "搜索关键词不能为空", nil)
		return
	}

	result, gErr := h.identityService.SearchTrademarkCategories(c.Request.Context(), keyword)
	if gErr != nil {
		log.Error().
			Err(gErr).
			Str("keyword", keyword).
			Msg("搜索商标类型失败")
		response.ErrorWithGlobalError(c, gErr)
		return
	}

	response.Success(c, "搜索商标类型成功", result)
}
