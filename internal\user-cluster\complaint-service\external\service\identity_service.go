package service

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/internal/user-cluster/complaint-service/types"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
)

// IdentityService 身份认证服务结构体
type IdentityService struct {
	identityRepo  repository.IdentityVerificationRepository
	countryRepo   repository.CountryRepository
	trademarkRepo repository.TrademarkCategoryRepository
	config        *types.Config
}

// NewIdentityService 创建身份认证服务实例
func NewIdentityService(
	identityRepo repository.IdentityVerificationRepository,
	countryRepo repository.CountryRepository,
	trademarkRepo repository.TrademarkCategoryRepository,
	config *types.Config,
) *IdentityService {
	return &IdentityService{
		identityRepo:  identityRepo,
		countryRepo:   countryRepo,
		trademarkRepo: trademarkRepo,
		config:        config,
	}
}

// CreateIdentityVerification 创建身份认证
func (s *IdentityService) CreateIdentityVerification(ctx context.Context, userKSUID string, req *dto.CreateIdentityVerificationRequest) (*dto.IdentityVerificationResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("type", string(req.Type)).
		Msg("开始创建身份认证")

	// 1. 参数验证
	if gErr := dto.ValidateCreateIdentityVerificationRequest(req); gErr != nil {
		return nil, gErr
	}

	// 2. 检查用户是否已有身份认证
	exists, err := s.identityRepo.ExistsByUserKSUID(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("检查用户身份认证失败")
		return nil, errors.NewInternalError("系统内部错误")
	}
	if exists {
		return nil, errors.NewBusinessError("您已提交过身份认证申请")
	}

	// 3. 创建身份认证记录
	verification := &model.IdentityVerification{
		VerificationKSUID: ksuid.GenerateKSUID(),
		UserKSUID:         userKSUID,
		Type:              req.Type,
		Status:            model.IdentityStatusPending,
		RealName:          req.RealName,
		PhoneNumber:       req.PhoneNumber,
		Email:             req.Email,
		IDCard:            req.IDCard,
		Address:           req.Address,
		PostalCode:        req.PostalCode,
		FaxNumber:         req.FaxNumber,
		LandlineNumber:    req.LandlineNumber,
		OrganizationName:  req.OrganizationName,
		CertificateNumber: req.CertificateNumber,
		ContactName:       req.ContactName,
		ContactIDCard:     req.ContactIDCard,
		ContactEmail:      req.ContactEmail,
		ContactPhone:      req.ContactPhone,
		ContactAddress:    req.ContactAddress,
		ContactPostalCode: req.ContactPostalCode,
		ContactFax:        req.ContactFax,
		ContactLandline:   req.ContactLandline,
	}

	// 4. 处理证件有效期
	if req.CertificateStartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.CertificateStartDate); err == nil {
			verification.CertificateStartAt = &startDate
		}
	}
	if req.CertificateEndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.CertificateEndDate); err == nil {
			verification.CertificateEndAt = &endDate
		}
	}

	// 5. 保存身份认证记录
	if err := s.identityRepo.Create(ctx, verification); err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("verification_ksuid", verification.VerificationKSUID).
			Msg("创建身份认证记录失败")
		return nil, errors.NewInternalError("创建身份认证失败")
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("verification_ksuid", verification.VerificationKSUID).
		Msg("创建身份认证成功")

	return dto.IdentityVerificationToResponse(verification), nil
}

// GetIdentityVerification 获取身份认证详情
func (s *IdentityService) GetIdentityVerification(ctx context.Context, userKSUID, verificationKSUID string) (*dto.IdentityVerificationResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("verification_ksuid", verificationKSUID).
		Msg("开始获取身份认证详情")

	verification, err := s.identityRepo.GetByKSUID(ctx, verificationKSUID)
	if err != nil {
		if err == repository.ErrIdentityVerificationNotFound {
			return nil, errors.NewNotFoundError("身份认证不存在")
		}
		log.Error().Err(err).Str("verification_ksuid", verificationKSUID).Msg("获取身份认证详情失败")
		return nil, errors.NewInternalError("获取身份认证详情失败")
	}

	// 检查权限：只有认证人或管理员可以查看
	if verification.UserKSUID != userKSUID {
		// 这里应该检查是否为管理员，简化处理
		return nil, errors.NewPermissionError("无权限查看此身份认证")
	}

	return dto.IdentityVerificationToResponse(verification), nil
}

// GetMyIdentityVerification 获取我的身份认证
func (s *IdentityService) GetMyIdentityVerification(ctx context.Context, userKSUID string) (*dto.IdentityVerificationResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Msg("开始获取我的身份认证")

	verification, err := s.identityRepo.GetByUserKSUID(ctx, userKSUID)
	if err != nil {
		if err == repository.ErrIdentityVerificationNotFound {
			return nil, errors.NewNotFoundError("您尚未提交身份认证")
		}
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("获取身份认证失败")
		return nil, errors.NewInternalError("获取身份认证失败")
	}

	return dto.IdentityVerificationToResponse(verification), nil
}

// UpdateIdentityVerification 更新身份认证
func (s *IdentityService) UpdateIdentityVerification(ctx context.Context, userKSUID, verificationKSUID string, req *dto.UpdateIdentityVerificationRequest) (*dto.IdentityVerificationResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("verification_ksuid", verificationKSUID).
		Msg("开始更新身份认证")

	// 1. 获取身份认证记录
	verification, err := s.identityRepo.GetByKSUID(ctx, verificationKSUID)
	if err != nil {
		if err == repository.ErrIdentityVerificationNotFound {
			return nil, errors.NewNotFoundError("身份认证不存在")
		}
		log.Error().Err(err).Str("verification_ksuid", verificationKSUID).Msg("获取身份认证记录失败")
		return nil, errors.NewInternalError("获取身份认证记录失败")
	}

	// 2. 检查权限
	if verification.UserKSUID != userKSUID {
		return nil, errors.NewPermissionError("无权限修改此身份认证")
	}

	// 3. 检查状态：只有待审核或被拒绝状态的认证可以修改
	if verification.Status != model.IdentityStatusPending && verification.Status != model.IdentityStatusRejected {
		return nil, errors.NewBusinessError("只有待审核或被拒绝状态的身份认证可以修改")
	}

	// 4. 更新字段
	if req.RealName != "" {
		verification.RealName = req.RealName
	}
	if req.PhoneNumber != "" {
		verification.PhoneNumber = req.PhoneNumber
	}
	if req.Email != "" {
		verification.Email = req.Email
	}
	if req.IDCard != "" {
		verification.IDCard = req.IDCard
	}
	if req.Address != "" {
		verification.Address = req.Address
	}
	if req.PostalCode != "" {
		verification.PostalCode = req.PostalCode
	}
	if req.FaxNumber != "" {
		verification.FaxNumber = req.FaxNumber
	}
	if req.LandlineNumber != "" {
		verification.LandlineNumber = req.LandlineNumber
	}
	if req.OrganizationName != "" {
		verification.OrganizationName = req.OrganizationName
	}
	if req.CertificateNumber != "" {
		verification.CertificateNumber = req.CertificateNumber
	}
	if req.CertificateStartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.CertificateStartDate); err == nil {
			verification.CertificateStartAt = &startDate
		}
	}
	if req.CertificateEndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.CertificateEndDate); err == nil {
			verification.CertificateEndAt = &endDate
		}
	}
	if req.ContactName != "" {
		verification.ContactName = req.ContactName
	}
	if req.ContactIDCard != "" {
		verification.ContactIDCard = req.ContactIDCard
	}
	if req.ContactEmail != "" {
		verification.ContactEmail = req.ContactEmail
	}
	if req.ContactPhone != "" {
		verification.ContactPhone = req.ContactPhone
	}
	if req.ContactAddress != "" {
		verification.ContactAddress = req.ContactAddress
	}
	if req.ContactPostalCode != "" {
		verification.ContactPostalCode = req.ContactPostalCode
	}
	if req.ContactFax != "" {
		verification.ContactFax = req.ContactFax
	}
	if req.ContactLandline != "" {
		verification.ContactLandline = req.ContactLandline
	}

	// 如果是被拒绝状态，重新设置为待审核
	if verification.Status == model.IdentityStatusRejected {
		verification.Status = model.IdentityStatusPending
		verification.ReviewerKSUID = ""
		verification.ReviewedAt = nil
		verification.ReviewNote = ""
		verification.RejectReason = ""
	}

	verification.UpdatedAt = time.Now()

	// 5. 保存更新
	if err := s.identityRepo.Update(ctx, verification); err != nil {
		log.Error().
			Err(err).
			Str("verification_ksuid", verificationKSUID).
			Msg("更新身份认证记录失败")
		return nil, errors.NewInternalError("更新身份认证失败")
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("verification_ksuid", verificationKSUID).
		Msg("更新身份认证成功")

	return dto.IdentityVerificationToResponse(verification), nil
}

// GetCountries 获取国家地区列表
func (s *IdentityService) GetCountries(ctx context.Context) ([]dto.CountryResponse, *errors.Errors) {
	log.Debug().Msg("开始获取国家地区列表")

	countries, err := s.countryRepo.GetActive(ctx)
	if err != nil {
		log.Error().Err(err).Msg("获取国家地区列表失败")
		return nil, errors.NewInternalError("获取国家地区列表失败")
	}

	// 转换为响应格式
	response := make([]dto.CountryResponse, 0, len(countries))
	for _, country := range countries {
		response = append(response, *dto.CountryToResponse(country))
	}

	return response, nil
}

// GetChinaRegions 获取中国特别行政区
func (s *IdentityService) GetChinaRegions(ctx context.Context) ([]dto.CountryResponse, *errors.Errors) {
	log.Debug().Msg("开始获取中国特别行政区列表")

	countries, err := s.countryRepo.GetChinaRegions(ctx)
	if err != nil {
		log.Error().Err(err).Msg("获取中国特别行政区列表失败")
		return nil, errors.NewInternalError("获取地区列表失败")
	}

	// 转换为响应格式
	response := make([]dto.CountryResponse, 0, len(countries))
	for _, country := range countries {
		response = append(response, *dto.CountryToResponse(country))
	}

	return response, nil
}

// GetTrademarkCategories 获取商标类型列表
func (s *IdentityService) GetTrademarkCategories(ctx context.Context) ([]dto.TrademarkCategoryResponse, *errors.Errors) {
	log.Debug().Msg("开始获取商标类型列表")

	categories, err := s.trademarkRepo.GetActive(ctx)
	if err != nil {
		log.Error().Err(err).Msg("获取商标类型列表失败")
		return nil, errors.NewInternalError("获取商标类型列表失败")
	}

	// 转换为响应格式
	response := make([]dto.TrademarkCategoryResponse, 0, len(categories))
	for _, category := range categories {
		response = append(response, *dto.TrademarkCategoryToResponse(category))
	}

	return response, nil
}

// SearchCountries 搜索国家地区
func (s *IdentityService) SearchCountries(ctx context.Context, keyword string) ([]dto.CountryResponse, *errors.Errors) {
	log.Debug().
		Str("keyword", keyword).
		Msg("开始搜索国家地区")

	if keyword == "" {
		return s.GetCountries(ctx)
	}

	countries, err := s.countryRepo.Search(ctx, keyword)
	if err != nil {
		log.Error().
			Err(err).
			Str("keyword", keyword).
			Msg("搜索国家地区失败")
		return nil, errors.NewInternalError("搜索国家地区失败")
	}

	// 转换为响应格式
	response := make([]dto.CountryResponse, 0, len(countries))
	for _, country := range countries {
		response = append(response, *dto.CountryToResponse(country))
	}

	return response, nil
}

// SearchTrademarkCategories 搜索商标类型
func (s *IdentityService) SearchTrademarkCategories(ctx context.Context, keyword string) ([]dto.TrademarkCategoryResponse, *errors.Errors) {
	log.Debug().
		Str("keyword", keyword).
		Msg("开始搜索商标类型")

	if keyword == "" {
		return s.GetTrademarkCategories(ctx)
	}

	categories, err := s.trademarkRepo.Search(ctx, keyword)
	if err != nil {
		log.Error().
			Err(err).
			Str("keyword", keyword).
			Msg("搜索商标类型失败")
		return nil, errors.NewInternalError("搜索商标类型失败")
	}

	// 转换为响应格式
	response := make([]dto.TrademarkCategoryResponse, 0, len(categories))
	for _, category := range categories {
		response = append(response, *dto.TrademarkCategoryToResponse(category))
	}

	return response, nil
}
