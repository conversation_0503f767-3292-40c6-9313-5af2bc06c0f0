package service

import (
	"context"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/internal/user-cluster/complaint-service/types"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
)

// RightsService 权益认证服务结构体
type RightsService struct {
	rightsRepo        repository.RightsVerificationRepository
	copyrightRepo     repository.CopyrightRepository
	trademarkRepo     repository.TrademarkRepository
	personalityRepo   repository.PersonalityRightRepository
	rightsEvidenceRepo repository.RightsEvidenceRepository
	countryRepo       repository.CountryRepository
	trademarkCatRepo  repository.TrademarkCategoryRepository
	config            *types.Config
}

// NewRightsService 创建权益认证服务实例
func NewRightsService(
	rightsRepo repository.RightsVerificationRepository,
	copyrightRepo repository.CopyrightRepository,
	trademarkRepo repository.TrademarkRepository,
	personalityRepo repository.PersonalityRightRepository,
	rightsEvidenceRepo repository.RightsEvidenceRepository,
	countryRepo repository.CountryRepository,
	trademarkCatRepo repository.TrademarkCategoryRepository,
	config *types.Config,
) *RightsService {
	return &RightsService{
		rightsRepo:         rightsRepo,
		copyrightRepo:      copyrightRepo,
		trademarkRepo:      trademarkRepo,
		personalityRepo:    personalityRepo,
		rightsEvidenceRepo: rightsEvidenceRepo,
		countryRepo:        countryRepo,
		trademarkCatRepo:   trademarkCatRepo,
		config:             config,
	}
}

// CreateRightsVerification 创建权益认证
func (s *RightsService) CreateRightsVerification(ctx context.Context, userKSUID string, req *dto.CreateRightsVerificationRequest) (*dto.RightsVerificationResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("type", string(req.Type)).
		Msg("开始创建权益认证")

	// 1. 参数验证
	if gErr := dto.ValidateCreateRightsVerificationRequest(req); gErr != nil {
		return nil, gErr
	}

	// 2. 检查用户权益认证数量限制
	count, err := s.rightsRepo.CountByUserKSUID(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("检查用户权益认证数量失败")
		return nil, errors.NewInternalError("系统内部错误")
	}
	if count >= int64(s.config.Complaint.Limits.MaxRightsPerUser) {
		return nil, errors.NewBusinessError("权益认证数量已达上限")
	}

	// 3. 创建权益认证记录
	verification := &model.RightsVerification{
		RightsKSUID:        ksuid.GenerateKSUID(),
		UserKSUID:          userKSUID,
		Type:               req.Type,
		Status:             model.RightsStatusPending,
		IsAgent:            req.IsAgent,
		AgentType:          req.AgentType,
		RightOwnerName:     req.RightOwnerName,
	}

	// 4. 处理授权期限
	if req.AuthorizationStartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.AuthorizationStartDate); err == nil {
			verification.AuthorizationStartAt = &startDate
		}
	}
	if req.AuthorizationEndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.AuthorizationEndDate); err == nil {
			verification.AuthorizationEndAt = &endDate
		}
	}

	// 5. 保存权益认证记录
	if err := s.rightsRepo.Create(ctx, verification); err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("rights_ksuid", verification.RightsKSUID).
			Msg("创建权益认证记录失败")
		return nil, errors.NewInternalError("创建权益认证失败")
	}

	// 6. 根据权益类型创建具体权益信息
	switch req.Type {
	case model.RightsTypeCopyright:
		if err := s.createCopyrights(ctx, verification.RightsKSUID, req.Copyrights); err != nil {
			return nil, err
		}
	case model.RightsTypeTrademark:
		if err := s.createTrademarks(ctx, verification.RightsKSUID, req.Trademarks); err != nil {
			return nil, err
		}
	case model.RightsTypePersonality:
		if err := s.createPersonalityRights(ctx, verification.RightsKSUID, req.PersonalityRights); err != nil {
			return nil, err
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("rights_ksuid", verification.RightsKSUID).
		Msg("创建权益认证成功")

	// 7. 重新获取完整的权益认证信息
	fullVerification, err := s.rightsRepo.GetByKSUID(ctx, verification.RightsKSUID)
	if err != nil {
		log.Error().Err(err).Str("rights_ksuid", verification.RightsKSUID).Msg("获取完整权益认证信息失败")
		return nil, errors.NewInternalError("获取权益认证详情失败")
	}

	return dto.RightsVerificationToResponse(fullVerification), nil
}

// createCopyrights 创建著作权信息
func (s *RightsService) createCopyrights(ctx context.Context, rightsKSUID string, copyrights []dto.CreateCopyrightRequest) *errors.Errors {
	for _, copyrightReq := range copyrights {
		copyright := &model.Copyright{
			RightsKSUID:   rightsKSUID,
			CopyrightType: copyrightReq.CopyrightType,
			WorkName:      copyrightReq.WorkName,
			CountryCode:   copyrightReq.CountryCode,
		}

		// 处理有效期
		if copyrightReq.ValidFromDate != "" {
			if validFrom, err := time.Parse("2006-01-02", copyrightReq.ValidFromDate); err == nil {
				copyright.ValidFrom = &validFrom
			}
		}
		if copyrightReq.ValidToDate != "" {
			if validTo, err := time.Parse("2006-01-02", copyrightReq.ValidToDate); err == nil {
				copyright.ValidTo = &validTo
			}
		}

		if err := s.copyrightRepo.Create(ctx, copyright); err != nil {
			log.Error().
				Err(err).
				Str("rights_ksuid", rightsKSUID).
				Str("work_name", copyright.WorkName).
				Msg("创建著作权信息失败")
			return errors.NewInternalError("创建著作权信息失败")
		}
	}
	return nil
}

// createTrademarks 创建商标权信息
func (s *RightsService) createTrademarks(ctx context.Context, rightsKSUID string, trademarks []dto.CreateTrademarkRequest) *errors.Errors {
	for _, trademarkReq := range trademarks {
		trademark := &model.Trademark{
			RightsKSUID:    rightsKSUID,
			TrademarkName:  trademarkReq.TrademarkName,
			CategoryNumber: trademarkReq.CategoryNumber,
			CountryCode:    trademarkReq.CountryCode,
		}

		// 处理有效期
		if trademarkReq.ValidFromDate != "" {
			if validFrom, err := time.Parse("2006-01-02", trademarkReq.ValidFromDate); err == nil {
				trademark.ValidFrom = &validFrom
			}
		}
		if trademarkReq.ValidToDate != "" {
			if validTo, err := time.Parse("2006-01-02", trademarkReq.ValidToDate); err == nil {
				trademark.ValidTo = &validTo
			}
		}

		if err := s.trademarkRepo.Create(ctx, trademark); err != nil {
			log.Error().
				Err(err).
				Str("rights_ksuid", rightsKSUID).
				Str("trademark_name", trademark.TrademarkName).
				Msg("创建商标权信息失败")
			return errors.NewInternalError("创建商标权信息失败")
		}
	}
	return nil
}

// createPersonalityRights 创建人格权信息
func (s *RightsService) createPersonalityRights(ctx context.Context, rightsKSUID string, personalityRights []dto.CreatePersonalityRightRequest) *errors.Errors {
	for _, personalityReq := range personalityRights {
		personalityRight := &model.PersonalityRight{
			RightsKSUID:           rightsKSUID,
			PersonalityRightsType: personalityReq.PersonalityRightsType,
		}

		if err := s.personalityRepo.Create(ctx, personalityRight); err != nil {
			log.Error().
				Err(err).
				Str("rights_ksuid", rightsKSUID).
				Str("personality_rights_type", string(personalityRight.PersonalityRightsType)).
				Msg("创建人格权信息失败")
			return errors.NewInternalError("创建人格权信息失败")
		}
	}
	return nil
}

// GetRightsVerification 获取权益认证详情
func (s *RightsService) GetRightsVerification(ctx context.Context, userKSUID, rightsKSUID string) (*dto.RightsVerificationResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("rights_ksuid", rightsKSUID).
		Msg("开始获取权益认证详情")

	verification, err := s.rightsRepo.GetByKSUID(ctx, rightsKSUID)
	if err != nil {
		if err == repository.ErrRightsVerificationNotFound {
			return nil, errors.NewNotFoundError("权益认证不存在")
		}
		log.Error().Err(err).Str("rights_ksuid", rightsKSUID).Msg("获取权益认证详情失败")
		return nil, errors.NewInternalError("获取权益认证详情失败")
	}

	// 检查权限：只有认证人或管理员可以查看
	if verification.UserKSUID != userKSUID {
		// 这里应该检查是否为管理员，简化处理
		return nil, errors.NewPermissionError("无权限查看此权益认证")
	}

	return dto.RightsVerificationToResponse(verification), nil
}

// GetMyRightsVerifications 获取我的权益认证列表
func (s *RightsService) GetMyRightsVerifications(ctx context.Context, userKSUID string, req *dto.GetRightsVerificationsRequest) (*dto.RightsVerificationListResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("开始获取我的权益认证列表")

	// 查询权益认证列表
	verifications, total, err := s.rightsRepo.GetByUserKSUID(ctx, userKSUID, req.Page, req.PageSize)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取我的权益认证列表失败")
		return nil, errors.NewInternalError("获取权益认证列表失败")
	}

	// 计算总页数
	totalPages := int(total) / req.PageSize
	if int(total)%req.PageSize > 0 {
		totalPages++
	}

	// 构建响应
	response := &dto.RightsVerificationListResponse{
		Verifications: make([]dto.RightsVerificationResponse, 0, len(verifications)),
		Total:         total,
		Page:          req.Page,
		PageSize:      req.PageSize,
		TotalPages:    totalPages,
	}

	// 转换权益认证记录
	for _, verification := range verifications {
		response.Verifications = append(response.Verifications, *dto.RightsVerificationToResponse(verification))
	}

	return response, nil
}
