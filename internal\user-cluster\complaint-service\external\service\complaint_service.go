package service

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/internal/user-cluster/complaint-service/types"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
)

// ComplaintService 投诉服务结构体
type ComplaintService struct {
	complaintRepo         repository.ComplaintRepository
	complaintEvidenceRepo repository.ComplaintEvidenceRepository
	violationCategoryRepo repository.ViolationCategoryRepository
	userServiceClient     UserServiceClient
	config                *types.Config
}

// UserServiceClient 用户服务客户端接口
type UserServiceClient interface {
	BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) error
	GetUserInfo(ctx context.Context, userKSUID string) (*UserInfo, error)
}

// UserInfo 用户信息
type UserInfo struct {
	UserKSUID string `json:"user_ksuid"`
	Nickname  string `json:"nickname"`
	Email     string `json:"email"`
}

// NewComplaintService 创建投诉服务实例
func NewComplaintService(
	complaintRepo repository.ComplaintRepository,
	complaintEvidenceRepo repository.ComplaintEvidenceRepository,
	violationCategoryRepo repository.ViolationCategoryRepository,
	userServiceClient UserServiceClient,
	config *types.Config,
) *ComplaintService {
	return &ComplaintService{
		complaintRepo:         complaintRepo,
		complaintEvidenceRepo: complaintEvidenceRepo,
		violationCategoryRepo: violationCategoryRepo,
		userServiceClient:     userServiceClient,
		config:                config,
	}
}

// CreateComplaint 创建投诉
func (s *ComplaintService) CreateComplaint(ctx context.Context, userKSUID string, req *dto.CreateComplaintRequest) (*dto.ComplaintResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("type", string(req.Type)).
		Msg("开始创建投诉")

	// 1. 参数验证
	if gErr := dto.ValidateCreateComplaintRequest(req); gErr != nil {
		return nil, gErr
	}

	// 2. 检查用户今日投诉次数限制
	todayCount, err := s.complaintRepo.CountTodayComplaintsByUser(ctx, userKSUID)
	if err != nil {
		log.Error().Err(err).Str("user_ksuid", userKSUID).Msg("检查今日投诉次数失败")
		return nil, errors.NewInternalError("系统内部错误")
	}
	if todayCount >= int64(s.config.Complaint.Limits.MaxComplaintsPerDay) {
		return nil, errors.NewBusinessError("今日投诉次数已达上限")
	}

	// 3. 检查是否已对该内容投诉过
	exists, err := s.complaintRepo.ExistsByContentKSUID(ctx, req.ContentKSUID, userKSUID)
	if err != nil {
		log.Error().Err(err).Msg("检查重复投诉失败")
		return nil, errors.NewInternalError("系统内部错误")
	}
	if exists {
		return nil, errors.NewBusinessError("您已对该内容提交过投诉")
	}

	// 4. 创建投诉记录
	complaint := &model.Complaint{
		ComplaintKSUID:   ksuid.GenerateKSUID(),
		UserKSUID:        userKSUID,
		ContentKSUID:     req.ContentKSUID,
		ContentType:      req.ContentType,
		Type:             req.Type,
		Status:           model.ComplaintStatusPending,
		Title:            req.Title,
		Description:      req.Description,
		UserRole:         req.UserRole,
		OriginalURL:      req.OriginalURL,
		WorkURL:          req.WorkURL,
		ViolationType:    req.ViolationType,
		ViolationSubType: req.ViolationSubType,
	}

	// 5. 保存投诉记录
	if err := s.complaintRepo.Create(ctx, complaint); err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("complaint_ksuid", complaint.ComplaintKSUID).
			Msg("创建投诉记录失败")
		return nil, errors.NewInternalError("创建投诉失败")
	}

	// 6. 如果是盗版投诉且需要拉黑用户
	if req.Type == model.ComplaintTypePiracy && req.BlockUserKSUID != "" {
		if err := s.userServiceClient.BlockUser(ctx, userKSUID, req.BlockUserKSUID); err != nil {
			log.Error().
				Err(err).
				Str("blocker_ksuid", userKSUID).
				Str("blocked_ksuid", req.BlockUserKSUID).
				Msg("拉黑用户失败")
			// 拉黑失败不影响投诉创建，只记录日志
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("complaint_ksuid", complaint.ComplaintKSUID).
		Msg("创建投诉成功")

	return dto.ComplaintToResponse(complaint), nil
}

// GetComplaint 获取投诉详情
func (s *ComplaintService) GetComplaint(ctx context.Context, userKSUID, complaintKSUID string) (*dto.ComplaintResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Str("complaint_ksuid", complaintKSUID).
		Msg("开始获取投诉详情")

	complaint, err := s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		if err == repository.ErrComplaintNotFound {
			return nil, errors.NewNotFoundError("投诉不存在")
		}
		log.Error().Err(err).Str("complaint_ksuid", complaintKSUID).Msg("获取投诉详情失败")
		return nil, errors.NewInternalError("获取投诉详情失败")
	}

	// 检查权限：只有投诉人或管理员可以查看
	if complaint.UserKSUID != userKSUID {
		// 这里应该检查是否为管理员，简化处理
		return nil, errors.NewPermissionError("无权限查看此投诉")
	}

	return dto.ComplaintToResponse(complaint), nil
}

// UpdateComplaint 更新投诉
func (s *ComplaintService) UpdateComplaint(ctx context.Context, userKSUID, complaintKSUID string, req *dto.UpdateComplaintRequest) (*dto.ComplaintResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("complaint_ksuid", complaintKSUID).
		Msg("开始更新投诉")

	// 1. 获取投诉记录
	complaint, err := s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		if err == repository.ErrComplaintNotFound {
			return nil, errors.NewNotFoundError("投诉不存在")
		}
		log.Error().Err(err).Str("complaint_ksuid", complaintKSUID).Msg("获取投诉记录失败")
		return nil, errors.NewInternalError("获取投诉记录失败")
	}

	// 2. 检查权限
	if complaint.UserKSUID != userKSUID {
		return nil, errors.NewPermissionError("无权限修改此投诉")
	}

	// 3. 检查状态：只有待处理状态的投诉可以修改
	if complaint.Status != model.ComplaintStatusPending {
		return nil, errors.NewBusinessError("只有待处理状态的投诉可以修改")
	}

	// 4. 更新字段
	if req.Title != "" {
		complaint.Title = req.Title
	}
	if req.Description != "" {
		complaint.Description = req.Description
	}
	if req.OriginalURL != "" {
		complaint.OriginalURL = req.OriginalURL
	}
	if req.WorkURL != "" {
		complaint.WorkURL = req.WorkURL
	}
	if req.ViolationType != "" {
		complaint.ViolationType = req.ViolationType
	}
	if req.ViolationSubType != "" {
		complaint.ViolationSubType = req.ViolationSubType
	}

	complaint.UpdatedAt = time.Now()

	// 5. 保存更新
	if err := s.complaintRepo.Update(ctx, complaint); err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaintKSUID).
			Msg("更新投诉记录失败")
		return nil, errors.NewInternalError("更新投诉失败")
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("complaint_ksuid", complaintKSUID).
		Msg("更新投诉成功")

	return dto.ComplaintToResponse(complaint), nil
}

// DeleteComplaint 删除投诉
func (s *ComplaintService) DeleteComplaint(ctx context.Context, userKSUID, complaintKSUID string) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("complaint_ksuid", complaintKSUID).
		Msg("开始删除投诉")

	// 1. 获取投诉记录
	complaint, err := s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		if err == repository.ErrComplaintNotFound {
			return errors.NewNotFoundError("投诉不存在")
		}
		log.Error().Err(err).Str("complaint_ksuid", complaintKSUID).Msg("获取投诉记录失败")
		return errors.NewInternalError("获取投诉记录失败")
	}

	// 2. 检查权限
	if complaint.UserKSUID != userKSUID {
		return errors.NewPermissionError("无权限删除此投诉")
	}

	// 3. 检查状态：只有待处理状态的投诉可以删除
	if complaint.Status != model.ComplaintStatusPending {
		return errors.NewBusinessError("只有待处理状态的投诉可以删除")
	}

	// 4. 删除投诉记录
	if err := s.complaintRepo.Delete(ctx, complaintKSUID); err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaintKSUID).
			Msg("删除投诉记录失败")
		return errors.NewInternalError("删除投诉失败")
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("complaint_ksuid", complaintKSUID).
		Msg("删除投诉成功")

	return nil
}

// GetMyComplaints 获取我的投诉列表
func (s *ComplaintService) GetMyComplaints(ctx context.Context, userKSUID string, req *dto.GetMyComplaintsRequest) (*dto.ComplaintListResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("开始获取我的投诉列表")

	// 构建过滤条件
	filters := repository.ComplaintFilters{
		Page:      req.Page,
		PageSize:  req.PageSize,
		Type:      req.Type,
		Status:    req.Status,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	// 查询投诉列表
	complaints, total, err := s.complaintRepo.GetMyComplaints(ctx, userKSUID, filters)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取我的投诉列表失败")
		return nil, errors.NewInternalError("获取投诉列表失败")
	}

	// 计算总页数
	totalPages := int(total) / req.PageSize
	if int(total)%req.PageSize > 0 {
		totalPages++
	}

	// 构建响应
	response := &dto.ComplaintListResponse{
		Complaints: make([]dto.ComplaintResponse, 0, len(complaints)),
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	// 转换投诉记录
	for _, complaint := range complaints {
		response.Complaints = append(response.Complaints, *dto.ComplaintToResponse(complaint))
	}

	return response, nil
}

// GetComplaintsAgainstMe 获取针对我的投诉列表
func (s *ComplaintService) GetComplaintsAgainstMe(ctx context.Context, userKSUID string, req *dto.GetComplaintsAgainstMeRequest) (*dto.ComplaintListResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("开始获取针对我的投诉列表")

	// 构建过滤条件
	filters := repository.ComplaintFilters{
		Page:      req.Page,
		PageSize:  req.PageSize,
		Status:    req.Status,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		SortBy:    "created_at",
		SortOrder: "desc",
	}

	// 查询投诉列表
	complaints, total, err := s.complaintRepo.GetComplaintsAgainstMe(ctx, userKSUID, filters)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取针对我的投诉列表失败")
		return nil, errors.NewInternalError("获取投诉列表失败")
	}

	// 计算总页数
	totalPages := int(total) / req.PageSize
	if int(total)%req.PageSize > 0 {
		totalPages++
	}

	// 构建响应
	response := &dto.ComplaintListResponse{
		Complaints: make([]dto.ComplaintResponse, 0, len(complaints)),
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	// 转换投诉记录
	for _, complaint := range complaints {
		response.Complaints = append(response.Complaints, *dto.ComplaintToResponse(complaint))
	}

	return response, nil
}

// ProcessComplaint 处理投诉
func (s *ComplaintService) ProcessComplaint(ctx context.Context, processorKSUID, complaintKSUID string, req *dto.ProcessComplaintRequest) (*dto.ComplaintResponse, *errors.Errors) {
	log.Info().
		Str("processor_ksuid", processorKSUID).
		Str("complaint_ksuid", complaintKSUID).
		Str("status", string(req.Status)).
		Msg("开始处理投诉")

	// 1. 获取投诉记录
	complaint, err := s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		if err == repository.ErrComplaintNotFound {
			return nil, errors.NewNotFoundError("投诉不存在")
		}
		log.Error().Err(err).Str("complaint_ksuid", complaintKSUID).Msg("获取投诉记录失败")
		return nil, errors.NewInternalError("获取投诉记录失败")
	}

	// 2. 检查状态：只有待处理状态的投诉可以处理
	if complaint.Status != model.ComplaintStatusPending {
		return nil, errors.NewBusinessError("该投诉已处理")
	}

	// 3. 更新投诉状态
	if err := s.complaintRepo.UpdateStatus(ctx, complaintKSUID, req.Status, processorKSUID, req.ProcessNote, req.Resolution); err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaintKSUID).
			Msg("更新投诉状态失败")
		return nil, errors.NewInternalError("处理投诉失败")
	}

	// 4. 重新获取更新后的投诉记录
	complaint, err = s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		log.Error().Err(err).Str("complaint_ksuid", complaintKSUID).Msg("获取更新后的投诉记录失败")
		return nil, errors.NewInternalError("获取投诉详情失败")
	}

	log.Info().
		Str("processor_ksuid", processorKSUID).
		Str("complaint_ksuid", complaintKSUID).
		Str("status", string(req.Status)).
		Msg("处理投诉成功")

	return dto.ComplaintToResponse(complaint), nil
}

// GetComplaintStats 获取投诉统计信息
func (s *ComplaintService) GetComplaintStats(ctx context.Context, userKSUID string) (*dto.ComplaintStatsResponse, *errors.Errors) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Msg("开始获取投诉统计信息")

	stats, err := s.complaintRepo.GetStats(ctx, userKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取投诉统计信息失败")
		return nil, errors.NewInternalError("获取统计信息失败")
	}

	response := &dto.ComplaintStatsResponse{
		TotalComplaints:     stats.TotalComplaints,
		PendingComplaints:   stats.PendingComplaints,
		CompletedComplaints: stats.CompletedComplaints,
		CancelledComplaints: stats.CancelledComplaints,
		MyComplaints:        stats.MyComplaints,
		ComplaintsAgainstMe: stats.ComplaintsAgainstMe,
	}

	return response, nil
}

// GetViolationCategories 获取违规类别树
func (s *ComplaintService) GetViolationCategories(ctx context.Context) ([]dto.ViolationCategoryResponse, *errors.Errors) {
	log.Debug().Msg("开始获取违规类别树")

	categories, err := s.violationCategoryRepo.GetCategoryTree(ctx)
	if err != nil {
		log.Error().Err(err).Msg("获取违规类别树失败")
		return nil, errors.NewInternalError("获取违规类别失败")
	}

	// 转换为响应格式
	response := make([]dto.ViolationCategoryResponse, 0, len(categories))
	for _, category := range categories {
		response = append(response, *s.convertViolationCategory(category))
	}

	return response, nil
}

// convertViolationCategory 转换违规类别为响应格式
func (s *ComplaintService) convertViolationCategory(category *model.ViolationCategory) *dto.ViolationCategoryResponse {
	resp := &dto.ViolationCategoryResponse{
		Code:        category.Code,
		Name:        category.Name,
		Description: category.Description,
		ParentCode:  category.ParentCode,
		SortOrder:   category.SortOrder,
		Children:    make([]dto.ViolationCategoryResponse, 0, len(category.Children)),
	}

	for _, child := range category.Children {
		childResp := s.convertViolationCategory(child)
		resp.Children = append(resp.Children, *childResp)
	}

	return resp
}

// ProcessComplaint 处理投诉（管理员）
func (s *ComplaintService) ProcessComplaint(ctx context.Context, complaintKSUID, adminKSUID string, req *dto.ProcessComplaintRequest) (*dto.ComplaintResponse, error) {
	// 获取投诉
	complaint, err := s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		if err == repository.ErrComplaintNotFound {
			return nil, errors.NewNotFoundError("投诉不存在")
		}
		return nil, err
	}

	// 检查投诉状态
	if complaint.Status != model.ComplaintStatusPending {
		return nil, errors.NewBadRequestError("投诉已处理，无法重复处理")
	}

	// 更新投诉状态
	err = s.complaintRepo.UpdateStatus(ctx, complaintKSUID, req.Status, adminKSUID, req.ProcessNote, req.Resolution)
	if err != nil {
		return nil, err
	}

	// 重新获取更新后的投诉
	complaint, err = s.complaintRepo.GetByKSUID(ctx, complaintKSUID)
	if err != nil {
		return nil, err
	}

	// 如果投诉通过，执行相应的处理逻辑
	if req.Status == model.ComplaintStatusApproved {
		// 这里可以添加投诉通过后的处理逻辑
		// 比如：拉黑用户、删除内容等
		log.Info().
			Str("complaint_ksuid", complaintKSUID).
			Str("accused_ksuid", complaint.AccusedKSUID).
			Str("content_ksuid", complaint.ContentKSUID).
			Msg("投诉通过，执行处理逻辑")
	}

	return dto.ComplaintToResponse(complaint), nil
}

// GetComplaintsWithFilters 根据过滤条件获取投诉列表（管理员）
func (s *ComplaintService) GetComplaintsWithFilters(ctx context.Context, filters dto.ComplaintFilters) (*dto.ComplaintListResponse, error) {
	// 转换过滤条件
	repoFilters := repository.ComplaintFilters{
		Page:           filters.Page,
		PageSize:       filters.PageSize,
		Status:         string(filters.Status),
		Type:           string(filters.Type),
		ComplainerKSUID: filters.ComplainerKSUID,
		AccusedKSUID:   filters.AccusedKSUID,
		ContentKSUID:   filters.ContentKSUID,
		StartDate:      filters.StartDate,
		EndDate:        filters.EndDate,
		SortBy:         filters.SortBy,
		SortOrder:      filters.SortOrder,
	}

	// 获取投诉列表
	complaints, total, err := s.complaintRepo.GetWithFilters(ctx, repoFilters)
	if err != nil {
		return nil, err
	}

	// 转换响应
	var complaintResponses []dto.ComplaintResponse
	for _, complaint := range complaints {
		complaintResponses = append(complaintResponses, *dto.ComplaintToResponse(complaint))
	}

	// 计算总页数
	totalPages := int((total + int64(filters.PageSize) - 1) / int64(filters.PageSize))

	return &dto.ComplaintListResponse{
		List:       complaintResponses,
		Total:      total,
		Page:       filters.Page,
		PageSize:   filters.PageSize,
		TotalPages: totalPages,
	}, nil
}
