package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// identityVerificationRepo 身份认证数据访问层实现
type identityVerificationRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewIdentityVerificationRepository 创建身份认证数据访问层实例
func NewIdentityVerificationRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.IdentityVerificationRepository {
	return &identityVerificationRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *identityVerificationRepo) GetDB() interface{} {
	return r.db
}

// Create 创建身份认证
func (r *identityVerificationRepo) Create(ctx context.Context, verification *model.IdentityVerification) error {
	log.Debug().
		Str("verification_ksuid", verification.VerificationKSUID).
		Str("user_ksuid", verification.UserKSUID).
		Msg("开始创建身份认证记录")

	err := r.db.WithContext(ctx).Create(verification).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("verification_ksuid", verification.VerificationKSUID).
			Msg("创建身份认证记录失败")
		return err
	}

	log.Debug().
		Str("verification_ksuid", verification.VerificationKSUID).
		Msg("创建身份认证记录成功")

	return nil
}

// GetByKSUID 根据KSUID获取身份认证
func (r *identityVerificationRepo) GetByKSUID(ctx context.Context, verificationKSUID string) (*model.IdentityVerification, error) {
	var verification model.IdentityVerification
	err := r.db.WithContext(ctx).
		Where("verification_ksuid = ?", verificationKSUID).
		First(&verification).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrIdentityVerificationNotFound
		}
		return nil, err
	}

	return &verification, nil
}

// GetByID 根据ID获取身份认证
func (r *identityVerificationRepo) GetByID(ctx context.Context, id uint) (*model.IdentityVerification, error) {
	var verification model.IdentityVerification
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&verification).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrIdentityVerificationNotFound
		}
		return nil, err
	}

	return &verification, nil
}

// Update 更新身份认证
func (r *identityVerificationRepo) Update(ctx context.Context, verification *model.IdentityVerification) error {
	log.Debug().
		Str("verification_ksuid", verification.VerificationKSUID).
		Msg("开始更新身份认证记录")

	err := r.db.WithContext(ctx).Save(verification).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("verification_ksuid", verification.VerificationKSUID).
			Msg("更新身份认证记录失败")
		return err
	}

	log.Debug().
		Str("verification_ksuid", verification.VerificationKSUID).
		Msg("更新身份认证记录成功")

	return nil
}

// Delete 删除身份认证
func (r *identityVerificationRepo) Delete(ctx context.Context, verificationKSUID string) error {
	log.Debug().
		Str("verification_ksuid", verificationKSUID).
		Msg("开始删除身份认证记录")

	err := r.db.WithContext(ctx).
		Where("verification_ksuid = ?", verificationKSUID).
		Delete(&model.IdentityVerification{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("verification_ksuid", verificationKSUID).
			Msg("删除身份认证记录失败")
		return err
	}

	log.Debug().
		Str("verification_ksuid", verificationKSUID).
		Msg("删除身份认证记录成功")

	return nil
}

// GetByUserKSUID 根据用户KSUID获取身份认证
func (r *identityVerificationRepo) GetByUserKSUID(ctx context.Context, userKSUID string) (*model.IdentityVerification, error) {
	var verification model.IdentityVerification
	err := r.db.WithContext(ctx).
		Where("user_ksuid = ?", userKSUID).
		Order("created_at DESC").
		First(&verification).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrIdentityVerificationNotFound
		}
		return nil, err
	}

	return &verification, nil
}

// GetByStatus 根据状态获取身份认证列表
func (r *identityVerificationRepo) GetByStatus(ctx context.Context, status model.IdentityStatus, page, pageSize int) ([]*model.IdentityVerification, int64, error) {
	var verifications []*model.IdentityVerification
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("status = ?", status).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Where("status = ?", status).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&verifications).Error

	if err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// GetByType 根据类型获取身份认证列表
func (r *identityVerificationRepo) GetByType(ctx context.Context, identityType model.IdentityType, page, pageSize int) ([]*model.IdentityVerification, int64, error) {
	var verifications []*model.IdentityVerification
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("type = ?", identityType).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Where("type = ?", identityType).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&verifications).Error

	if err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// GetWithFilters 根据过滤条件获取身份认证列表
func (r *identityVerificationRepo) GetWithFilters(ctx context.Context, filters repository.IdentityFilters) ([]*model.IdentityVerification, int64, error) {
	var verifications []*model.IdentityVerification
	var total int64

	query := r.db.WithContext(ctx).Model(&model.IdentityVerification{})

	// 应用过滤条件
	query = r.applyFilters(query, filters)

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (filters.Page - 1) * filters.PageSize
	query = r.db.WithContext(ctx)
	query = r.applyFilters(query, filters)

	// 应用排序
	orderBy := "created_at DESC"
	if filters.SortBy != "" {
		direction := "DESC"
		if filters.SortOrder == "asc" {
			direction = "ASC"
		}
		orderBy = fmt.Sprintf("%s %s", filters.SortBy, direction)
	}

	err = query.Order(orderBy).
		Offset(offset).
		Limit(filters.PageSize).
		Find(&verifications).Error

	if err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// applyFilters 应用查询过滤条件
func (r *identityVerificationRepo) applyFilters(query *gorm.DB, filters repository.IdentityFilters) *gorm.DB {
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.UserKSUID != "" {
		query = query.Where("user_ksuid = ?", filters.UserKSUID)
	}
	if filters.ReviewerKSUID != "" {
		query = query.Where("reviewer_ksuid = ?", filters.ReviewerKSUID)
	}
	if filters.StartDate != "" {
		query = query.Where("created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != "" {
		query = query.Where("created_at <= ?", filters.EndDate+" 23:59:59")
	}

	return query
}

// CountByUserKSUID 统计用户身份认证数
func (r *identityVerificationRepo) CountByUserKSUID(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("user_ksuid = ?", userKSUID).
		Count(&count).Error
	return count, err
}

// CountByStatus 统计指定状态的身份认证数
func (r *identityVerificationRepo) CountByStatus(ctx context.Context, status model.IdentityStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("status = ?", status).
		Count(&count).Error
	return count, err
}

// CountByType 统计指定类型的身份认证数
func (r *identityVerificationRepo) CountByType(ctx context.Context, identityType model.IdentityType) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("type = ?", identityType).
		Count(&count).Error
	return count, err
}

// UpdateStatus 更新身份认证状态
func (r *identityVerificationRepo) UpdateStatus(ctx context.Context, verificationKSUID string, status model.IdentityStatus, reviewerKSUID, reviewNote, rejectReason string) error {
	updates := map[string]interface{}{
		"status":        status,
		"reviewer_ksuid": reviewerKSUID,
		"review_note":   reviewNote,
		"reject_reason": rejectReason,
		"reviewed_at":   time.Now(),
		"updated_at":    time.Now(),
	}

	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("verification_ksuid = ?", verificationKSUID).
		Updates(updates).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("verification_ksuid", verificationKSUID).
			Msg("更新身份认证状态失败")
		return err
	}

	return nil
}

// ExistsByUserKSUID 检查用户是否已有身份认证
func (r *identityVerificationRepo) ExistsByUserKSUID(ctx context.Context, userKSUID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("user_ksuid = ?", userKSUID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetPendingVerifications 获取待审核的身份认证列表
func (r *identityVerificationRepo) GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.IdentityVerification, int64, error) {
	return r.GetByStatus(ctx, model.IdentityStatusPending, page, pageSize)
}

// GetExpiredVerifications 获取已过期的身份认证列表
func (r *identityVerificationRepo) GetExpiredVerifications(ctx context.Context) ([]*model.IdentityVerification, error) {
	var verifications []*model.IdentityVerification
	err := r.db.WithContext(ctx).
		Where("status = ? AND certificate_end_at < ?", model.IdentityStatusApproved, time.Now()).
		Find(&verifications).Error

	if err != nil {
		return nil, err
	}

	return verifications, nil
}

// IsUserVerified 检查用户是否已通过身份认证
func (r *identityVerificationRepo) IsUserVerified(ctx context.Context, userKSUID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.IdentityVerification{}).
		Where("user_ksuid = ? AND status = ?", userKSUID, model.IdentityStatusApproved).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
