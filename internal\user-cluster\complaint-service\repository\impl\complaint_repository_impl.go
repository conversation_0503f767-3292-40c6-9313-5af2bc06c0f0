package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// complaintRepo 投诉数据访问层实现
type complaintRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewComplaintRepository 创建投诉数据访问层实例
func NewComplaintRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.ComplaintRepository {
	return &complaintRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *complaintRepo) GetDB() interface{} {
	return r.db
}

// Create 创建投诉
func (r *complaintRepo) Create(ctx context.Context, complaint *model.Complaint) error {
	log.Debug().
		Str("complaint_ksuid", complaint.ComplaintKSUID).
		Str("user_ksuid", complaint.UserKSUID).
		Msg("开始创建投诉记录")

	err := r.db.WithContext(ctx).Create(complaint).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaint.ComplaintKSUID).
			Msg("创建投诉记录失败")
		return err
	}

	log.Debug().
		Str("complaint_ksuid", complaint.ComplaintKSUID).
		Msg("创建投诉记录成功")

	return nil
}

// GetByKSUID 根据KSUID获取投诉
func (r *complaintRepo) GetByKSUID(ctx context.Context, complaintKSUID string) (*model.Complaint, error) {
	var complaint model.Complaint
	err := r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("complaint_ksuid = ?", complaintKSUID).
		First(&complaint).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrComplaintNotFound
		}
		return nil, err
	}

	return &complaint, nil
}

// GetByID 根据ID获取投诉
func (r *complaintRepo) GetByID(ctx context.Context, id uint) (*model.Complaint, error) {
	var complaint model.Complaint
	err := r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("id = ?", id).
		First(&complaint).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrComplaintNotFound
		}
		return nil, err
	}

	return &complaint, nil
}

// Update 更新投诉
func (r *complaintRepo) Update(ctx context.Context, complaint *model.Complaint) error {
	log.Debug().
		Str("complaint_ksuid", complaint.ComplaintKSUID).
		Msg("开始更新投诉记录")

	err := r.db.WithContext(ctx).Save(complaint).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaint.ComplaintKSUID).
			Msg("更新投诉记录失败")
		return err
	}

	log.Debug().
		Str("complaint_ksuid", complaint.ComplaintKSUID).
		Msg("更新投诉记录成功")

	return nil
}

// Delete 删除投诉
func (r *complaintRepo) Delete(ctx context.Context, complaintKSUID string) error {
	log.Debug().
		Str("complaint_ksuid", complaintKSUID).
		Msg("开始删除投诉记录")

	err := r.db.WithContext(ctx).
		Where("complaint_ksuid = ?", complaintKSUID).
		Delete(&model.Complaint{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaintKSUID).
			Msg("删除投诉记录失败")
		return err
	}

	log.Debug().
		Str("complaint_ksuid", complaintKSUID).
		Msg("删除投诉记录成功")

	return nil
}

// GetByUserKSUID 根据用户KSUID获取投诉列表
func (r *complaintRepo) GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.Complaint, int64, error) {
	var complaints []*model.Complaint
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("user_ksuid = ?", userKSUID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("user_ksuid = ?", userKSUID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&complaints).Error

	if err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}

// GetByContentKSUID 根据内容KSUID获取投诉列表
func (r *complaintRepo) GetByContentKSUID(ctx context.Context, contentKSUID string, page, pageSize int) ([]*model.Complaint, int64, error) {
	var complaints []*model.Complaint
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("content_ksuid = ?", contentKSUID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("content_ksuid = ?", contentKSUID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&complaints).Error

	if err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}

// GetByStatus 根据状态获取投诉列表
func (r *complaintRepo) GetByStatus(ctx context.Context, status model.ComplaintStatus, page, pageSize int) ([]*model.Complaint, int64, error) {
	var complaints []*model.Complaint
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("status = ?", status).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("status = ?", status).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&complaints).Error

	if err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}

// GetByType 根据类型获取投诉列表
func (r *complaintRepo) GetByType(ctx context.Context, complaintType model.ComplaintType, page, pageSize int) ([]*model.Complaint, int64, error) {
	var complaints []*model.Complaint
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("type = ?", complaintType).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("type = ?", complaintType).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&complaints).Error

	if err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}

// GetWithFilters 根据过滤条件获取投诉列表
func (r *complaintRepo) GetWithFilters(ctx context.Context, filters repository.ComplaintFilters) ([]*model.Complaint, int64, error) {
	var complaints []*model.Complaint
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Complaint{})

	// 应用过滤条件
	query = r.applyFilters(query, filters)

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (filters.Page - 1) * filters.PageSize
	query = r.db.WithContext(ctx).Preload("EvidenceFiles")
	query = r.applyFilters(query, filters)

	// 应用排序
	orderBy := "created_at DESC"
	if filters.SortBy != "" {
		direction := "DESC"
		if filters.SortOrder == "asc" {
			direction = "ASC"
		}
		orderBy = fmt.Sprintf("%s %s", filters.SortBy, direction)
	}

	err = query.Order(orderBy).
		Offset(offset).
		Limit(filters.PageSize).
		Find(&complaints).Error

	if err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}

// applyFilters 应用查询过滤条件
func (r *complaintRepo) applyFilters(query *gorm.DB, filters repository.ComplaintFilters) *gorm.DB {
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.ContentType != "" {
		query = query.Where("content_type = ?", filters.ContentType)
	}
	if filters.UserKSUID != "" {
		query = query.Where("user_ksuid = ?", filters.UserKSUID)
	}
	if filters.ProcessorKSUID != "" {
		query = query.Where("processor_ksuid = ?", filters.ProcessorKSUID)
	}
	if filters.StartDate != "" {
		query = query.Where("created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != "" {
		query = query.Where("created_at <= ?", filters.EndDate+" 23:59:59")
	}

	return query
}

// GetMyComplaints 获取我的投诉列表
func (r *complaintRepo) GetMyComplaints(ctx context.Context, userKSUID string, filters repository.ComplaintFilters) ([]*model.Complaint, int64, error) {
	filters.UserKSUID = userKSUID
	return r.GetWithFilters(ctx, filters)
}

// GetComplaintsAgainstMe 获取针对我的投诉列表
func (r *complaintRepo) GetComplaintsAgainstMe(ctx context.Context, userKSUID string, filters repository.ComplaintFilters) ([]*model.Complaint, int64, error) {
	var complaints []*model.Complaint
	var total int64

	// 这里需要通过content_ksuid关联到具体的内容服务来查找属于该用户的内容
	// 由于跨服务查询复杂，这里先简化实现，实际应该通过内容服务API查询
	query := r.db.WithContext(ctx).Model(&model.Complaint{})

	// 应用其他过滤条件
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.StartDate != "" {
		query = query.Where("created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != "" {
		query = query.Where("created_at <= ?", filters.EndDate+" 23:59:59")
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (filters.Page - 1) * filters.PageSize
	err = r.db.WithContext(ctx).
		Preload("EvidenceFiles").
		Where("status = ? OR status = ?", filters.Status, "").
		Order("created_at DESC").
		Offset(offset).
		Limit(filters.PageSize).
		Find(&complaints).Error

	if err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}

// CountByUserKSUID 统计用户投诉数
func (r *complaintRepo) CountByUserKSUID(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("user_ksuid = ?", userKSUID).
		Count(&count).Error
	return count, err
}

// CountByStatus 统计指定状态的投诉数
func (r *complaintRepo) CountByStatus(ctx context.Context, status model.ComplaintStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("status = ?", status).
		Count(&count).Error
	return count, err
}

// CountByType 统计指定类型的投诉数
func (r *complaintRepo) CountByType(ctx context.Context, complaintType model.ComplaintType) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("type = ?", complaintType).
		Count(&count).Error
	return count, err
}

// GetStats 获取投诉统计信息
func (r *complaintRepo) GetStats(ctx context.Context, userKSUID string) (*repository.ComplaintStats, error) {
	stats := &repository.ComplaintStats{}

	// 总投诉数
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Count(&stats.TotalComplaints).Error
	if err != nil {
		return nil, err
	}

	// 待处理投诉数
	err = r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("status = ?", model.ComplaintStatusPending).
		Count(&stats.PendingComplaints).Error
	if err != nil {
		return nil, err
	}

	// 已完成投诉数
	err = r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("status = ?", model.ComplaintStatusCompleted).
		Count(&stats.CompletedComplaints).Error
	if err != nil {
		return nil, err
	}

	// 已取消投诉数
	err = r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("status = ?", model.ComplaintStatusCancelled).
		Count(&stats.CancelledComplaints).Error
	if err != nil {
		return nil, err
	}

	// 我的投诉数
	if userKSUID != "" {
		err = r.db.WithContext(ctx).
			Model(&model.Complaint{}).
			Where("user_ksuid = ?", userKSUID).
			Count(&stats.MyComplaints).Error
		if err != nil {
			return nil, err
		}

		// 今日投诉数
		today := time.Now().Format("2006-01-02")
		err = r.db.WithContext(ctx).
			Model(&model.Complaint{}).
			Where("user_ksuid = ? AND DATE(created_at) = ?", userKSUID, today).
			Count(&stats.TodayComplaints).Error
		if err != nil {
			return nil, err
		}
	}

	return stats, nil
}

// CountTodayComplaintsByUser 统计用户今日投诉数
func (r *complaintRepo) CountTodayComplaintsByUser(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	today := time.Now().Format("2006-01-02")
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("user_ksuid = ? AND DATE(created_at) = ?", userKSUID, today).
		Count(&count).Error
	return count, err
}

// UpdateStatus 更新投诉状态
func (r *complaintRepo) UpdateStatus(ctx context.Context, complaintKSUID string, status model.ComplaintStatus, processorKSUID, processNote, resolution string) error {
	updates := map[string]interface{}{
		"status":         status,
		"processor_ksuid": processorKSUID,
		"process_note":   processNote,
		"resolution":     resolution,
		"processed_at":   time.Now(),
		"updated_at":     time.Now(),
	}

	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("complaint_ksuid = ?", complaintKSUID).
		Updates(updates).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaintKSUID).
			Msg("更新投诉状态失败")
		return err
	}

	return nil
}

// ExistsByContentKSUID 检查是否已存在对该内容的投诉
func (r *complaintRepo) ExistsByContentKSUID(ctx context.Context, contentKSUID, userKSUID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("content_ksuid = ? AND user_ksuid = ?", contentKSUID, userKSUID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetPendingComplaints 获取待处理投诉列表
func (r *complaintRepo) GetPendingComplaints(ctx context.Context, page, pageSize int) ([]*model.Complaint, int64, error) {
	return r.GetByStatus(ctx, model.ComplaintStatusPending, page, pageSize)
}

// CountByComplainerKSUID 统计用户投诉数
func (r *complaintRepo) CountByComplainerKSUID(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("complainer_ksuid = ?", userKSUID).
		Count(&count).Error
	return count, err
}

// CountByComplainerKSUIDAndStatus 统计用户指定状态的投诉数
func (r *complaintRepo) CountByComplainerKSUIDAndStatus(ctx context.Context, userKSUID string, status model.ComplaintStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("complainer_ksuid = ? AND status = ?", userKSUID, status).
		Count(&count).Error
	return count, err
}

// CountByAccusedKSUID 统计针对用户的投诉数
func (r *complaintRepo) CountByAccusedKSUID(ctx context.Context, userKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("accused_ksuid = ?", userKSUID).
		Count(&count).Error
	return count, err
}

// CountByContentKSUID 统计内容投诉数
func (r *complaintRepo) CountByContentKSUID(ctx context.Context, contentKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("content_ksuid = ?", contentKSUID).
		Count(&count).Error
	return count, err
}

// CountByContentKSUIDAndStatus 统计内容指定状态的投诉数
func (r *complaintRepo) CountByContentKSUIDAndStatus(ctx context.Context, contentKSUID string, status model.ComplaintStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("content_ksuid = ? AND status = ?", contentKSUID, status).
		Count(&count).Error
	return count, err
}

// GetByContentKSUIDAndStatus 根据内容KSUID和状态获取投诉列表
func (r *complaintRepo) GetByContentKSUIDAndStatus(ctx context.Context, contentKSUID string, status model.ComplaintStatus, page, pageSize int) ([]*model.Complaint, int64, error) {
	var complaints []*model.Complaint
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&model.Complaint{}).
		Where("content_ksuid = ? AND status = ?", contentKSUID, status).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Preload("Evidences").
		Where("content_ksuid = ? AND status = ?", contentKSUID, status).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&complaints).Error

	if err != nil {
		return nil, 0, err
	}

	return complaints, total, nil
}
