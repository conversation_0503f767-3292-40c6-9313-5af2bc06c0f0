package types

import (
	"time"
)

// OperationType 操作类型枚举
type OperationType string

const (
	OperationTypeCreate       OperationType = "create"
	OperationTypeUpdate       OperationType = "update"
	OperationTypeDelete       OperationType = "delete"
	OperationTypeBatchUpdate  OperationType = "batch_update"
	OperationTypeBatchDelete  OperationType = "batch_delete"
	OperationTypeStatusChange OperationType = "status_change"
)

// TargetType 目标类型枚举
type TargetType string

const (
	TargetTypeContent  TargetType = "content"
	TargetTypeVideo    TargetType = "video"
	TargetTypeNovel    TargetType = "novel"
	TargetTypeMusic    TargetType = "music"
	TargetTypeUser     TargetType = "user"
	TargetTypeCategory TargetType = "category"
	TargetTypeTag      TargetType = "tag"
)

// BatchUpdateStatusRequest 批量更新状态请求
type BatchUpdateStatusRequest struct {
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
	Status        string   `json:"status" binding:"required"`
	Reason        string   `json:"reason,omitempty"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
	Reason        string   `json:"reason,omitempty"`
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	SuccessCount int                    `json:"success_count"`
	FailureCount int                    `json:"failure_count"`
	Failures     []BatchOperationError  `json:"failures,omitempty"`
	TotalCount   int                    `json:"total_count"`
}

// BatchOperationError 批量操作错误
type BatchOperationError struct {
	ContentKSUID string `json:"content_ksuid"`
	Error        string `json:"error"`
}

// ContentUpdateRequest 内容更新请求
type ContentUpdateRequest struct {
	Title       string   `json:"title,omitempty"`
	Description string   `json:"description,omitempty"`
	Status      string   `json:"status,omitempty"`
	CategoryID  uint     `json:"category_id,omitempty"`
	Tags        []string `json:"tags,omitempty"`
}

// UserContentFilters 用户内容过滤器
type UserContentFilters struct {
	ContentFilters
	IncludeDeleted bool `json:"include_deleted,omitempty"` // 是否包含已删除内容
}

// ContentSearchRequest 内容搜索请求
type ContentSearchRequest struct {
	Query        string   `json:"query" binding:"required"`
	ContentTypes []string `json:"content_types,omitempty"`
	Status       string   `json:"status,omitempty"`
	CategoryID   uint     `json:"category_id,omitempty"`
	Tags         []string `json:"tags,omitempty"`
	
	// 时间范围
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
	
	// 排序和分页
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
	Page      int    `json:"page,omitempty"`
	Limit     int    `json:"limit,omitempty"`
}

// ContentSearchResult 内容搜索结果
type ContentSearchResult struct {
	Contents   []*BaseContent `json:"contents"`
	Total      int            `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
	Query      string         `json:"query"`
}

// ContentStatsOverview 内容统计概览
type ContentStatsOverview struct {
	TotalContents    int64                    `json:"total_contents"`
	ContentsByType   map[string]int64         `json:"contents_by_type"`
	ContentsByStatus map[string]int64         `json:"contents_by_status"`
	RecentContents   int64                    `json:"recent_contents"`    // 最近7天新增
	ActiveUsers      int64                    `json:"active_users"`       // 活跃用户数
	TotalViews       int64                    `json:"total_views"`
	TotalLikes       int64                    `json:"total_likes"`
	TotalComments    int64                    `json:"total_comments"`
	TotalFavorites   int64                    `json:"total_favorites"`
}

// ContentTypeStats 内容类型统计
type ContentTypeStats struct {
	ContentType   string  `json:"content_type"`
	TotalCount    int64   `json:"total_count"`
	PublishedCount int64  `json:"published_count"`
	DraftCount    int64   `json:"draft_count"`
	ArchivedCount int64   `json:"archived_count"`
	DeletedCount  int64   `json:"deleted_count"`
	TotalViews    int64   `json:"total_views"`
	TotalLikes    int64   `json:"total_likes"`
	TotalComments int64   `json:"total_comments"`
	TotalFavorites int64  `json:"total_favorites"`
	AvgViews      float64 `json:"avg_views"`
	AvgLikes      float64 `json:"avg_likes"`
}

// UserContentStats 用户内容统计
type UserContentStats struct {
	UserKSUID      string                   `json:"user_ksuid"`
	TotalContents  int64                    `json:"total_contents"`
	ContentsByType map[string]int64         `json:"contents_by_type"`
	ContentsByStatus map[string]int64       `json:"contents_by_status"`
	TotalViews     int64                    `json:"total_views"`
	TotalLikes     int64                    `json:"total_likes"`
	TotalComments  int64                    `json:"total_comments"`
	TotalFavorites int64                    `json:"total_favorites"`
	FirstContentAt *time.Time               `json:"first_content_at,omitempty"`
	LastContentAt  *time.Time               `json:"last_content_at,omitempty"`
}

// TrendData 趋势数据
type TrendData struct {
	Date  string `json:"date"`  // YYYY-MM-DD格式
	Value int64  `json:"value"`
}

// ContentTrends 内容趋势分析
type ContentTrends struct {
	Period        string      `json:"period"`         // daily, weekly, monthly
	ContentCounts []TrendData `json:"content_counts"` // 内容数量趋势
	ViewCounts    []TrendData `json:"view_counts"`    // 浏览量趋势
	LikeCounts    []TrendData `json:"like_counts"`    // 点赞数趋势
	CommentCounts []TrendData `json:"comment_counts"` // 评论数趋势
}

// CategoryStats 分类统计
type CategoryStats struct {
	CategoryID    uint   `json:"category_id"`
	CategoryName  string `json:"category_name"`
	ContentCount  int64  `json:"content_count"`
	TotalViews    int64  `json:"total_views"`
	TotalLikes    int64  `json:"total_likes"`
	TotalComments int64  `json:"total_comments"`
}

// TagStats 标签统计
type TagStats struct {
	TagName       string `json:"tag_name"`
	ContentCount  int64  `json:"content_count"`
	TotalViews    int64  `json:"total_views"`
	TotalLikes    int64  `json:"total_likes"`
	PopularityScore float64 `json:"popularity_score"` // 热度评分
}

// OperationLogQuery 操作日志查询
type OperationLogQuery struct {
	OperatorKSUID string         `json:"operator_ksuid,omitempty"`
	OperationType OperationType  `json:"operation_type,omitempty"`
	TargetType    TargetType     `json:"target_type,omitempty"`
	TargetKSUID   string         `json:"target_ksuid,omitempty"`
	
	// 时间范围
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
	
	// 分页
	Page  int `json:"page,omitempty"`
	Limit int `json:"limit,omitempty"`
}

// ServiceClients 服务客户端集合
type ServiceClients struct {
	VideoClient       VideoServiceClient
	NovelClient       NovelServiceClient
	MusicClient       MusicServiceClient
	InteractionClient InteractionServiceClient
}

// VideoServiceClient 视频服务客户端接口
type VideoServiceClient interface {
	// 内容查询
	GetContentByKSUID(contentKSUID string) (interface{}, error)
	GetUserContents(userKSUID string, filters *ContentFilters) (interface{}, error)
	GetContentsByStatus(status string, filters *ContentFilters) (interface{}, error)
	BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]interface{}, error)
	GetContentsByFilters(filters *ContentFilters) (interface{}, error)

	// 内容操作
	UpdateContentStatus(contentKSUID string, status string) error
	DeleteContent(contentKSUID string) error
	BatchUpdateStatus(contentKSUIDs []string, status string) error
	BatchDeleteContents(contentKSUIDs []string) error

	// 统计查询（不包含点赞和收藏，这些在interaction-service中）
	GetContentStats(contentKSUID string) (interface{}, error)
	GetUserStats(userKSUID string) (interface{}, error)
	GetOverallStats() (interface{}, error)
}

// NovelServiceClient 小说服务客户端接口
type NovelServiceClient interface {
	// 内容查询
	GetContentByKSUID(contentKSUID string) (interface{}, error)
	GetUserContents(userKSUID string, filters *ContentFilters) (interface{}, error)
	GetContentsByStatus(status string, filters *ContentFilters) (interface{}, error)
	BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]interface{}, error)
	GetContentsByFilters(filters *ContentFilters) (interface{}, error)

	// 内容操作
	UpdateContentStatus(contentKSUID string, status string) error
	DeleteContent(contentKSUID string) error
	BatchUpdateStatus(contentKSUIDs []string, status string) error
	BatchDeleteContents(contentKSUIDs []string) error

	// 统计查询
	GetContentStats(contentKSUID string) (interface{}, error)
	GetUserStats(userKSUID string) (interface{}, error)
	GetOverallStats() (interface{}, error)
}

// MusicServiceClient 音乐服务客户端接口
type MusicServiceClient interface {
	// 内容查询
	GetContentByKSUID(contentKSUID string) (interface{}, error)
	GetUserContents(userKSUID string, filters *ContentFilters) (interface{}, error)
	GetContentsByStatus(status string, filters *ContentFilters) (interface{}, error)
	BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]interface{}, error)
	GetContentsByFilters(filters *ContentFilters) (interface{}, error)

	// 内容操作
	UpdateContentStatus(contentKSUID string, status string) error
	DeleteContent(contentKSUID string) error
	BatchUpdateStatus(contentKSUIDs []string, status string) error
	BatchDeleteContents(contentKSUIDs []string) error

	// 统计查询
	GetContentStats(contentKSUID string) (interface{}, error)
	GetUserStats(userKSUID string) (interface{}, error)
	GetOverallStats() (interface{}, error)
}

// InteractionServiceClient 交互服务客户端接口
type InteractionServiceClient interface {
	// 点赞统计
	GetContentLikeStats(contentKSUID string) (*LikeStats, error)
	BatchGetContentLikeStats(contentKSUIDs []string) (map[string]*LikeStats, error)

	// 收藏统计
	GetContentFavoriteStats(contentKSUID string) (*FavoriteStats, error)
	BatchGetContentFavoriteStats(contentKSUIDs []string) (map[string]*FavoriteStats, error)

	// 综合统计
	GetContentInteractionStats(contentKSUID string) (*InteractionStats, error)
	BatchGetContentInteractionStats(contentKSUIDs []string) (map[string]*InteractionStats, error)

	// 用户交互统计
	GetUserInteractionStats(userKSUID string) (*UserInteractionStats, error)

	// 总体统计
	GetOverallInteractionStats() (*OverallInteractionStats, error)
}

// ContentDetailsFactory 内容详情工厂接口
type ContentDetailsFactory interface {
	CreateDetails(contentType string) interface{}
}

// ContentConverter 内容转换器接口
type ContentConverter interface {
	ConvertVideoToBase(video interface{}) *BaseContent
	ConvertVideoToBaseWithInteraction(video interface{}, interaction *InteractionStats) *BaseContent
	ConvertVideoToDetails(video interface{}) *ContentWithDetails
	ConvertNovelToBase(novel interface{}) *BaseContent
	ConvertNovelToBaseWithInteraction(novel interface{}, interaction *InteractionStats) *BaseContent
	ConvertNovelToDetails(novel interface{}) *ContentWithDetails
}

// CacheConfig 缓存配置
type CacheConfig struct {
	ContentTTL     time.Duration `mapstructure:"content_ttl"`
	InteractionTTL time.Duration `mapstructure:"interaction_ttl"`
	UserContentTTL time.Duration `mapstructure:"user_content_ttl"`
	StatsTTL       time.Duration `mapstructure:"stats_ttl"`
}

// LimitsConfig 限制配置
type LimitsConfig struct {
	MaxBatchSize        int `mapstructure:"max_batch_size"`
	MaxSearchResults    int `mapstructure:"max_search_results"`
	MaxPageSize         int `mapstructure:"max_page_size"`
	DefaultPageSize     int `mapstructure:"default_page_size"`
}

// ManagementConfig 管理配置
type ManagementConfig struct {
	Cache  CacheConfig  `mapstructure:"cache"`
	Limits LimitsConfig `mapstructure:"limits"`
}
