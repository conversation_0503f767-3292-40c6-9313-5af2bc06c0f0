package test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"pxpat-backend/internal/user-cluster/complaint-service/dto"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
)

// MockComplaintRepository 模拟投诉Repository
type MockComplaintRepository struct {
	mock.Mock
}

func (m *MockComplaintRepository) GetDB() interface{} {
	args := m.Called()
	return args.Get(0)
}

func (m *MockComplaintRepository) Create(ctx context.Context, complaint *model.Complaint) error {
	args := m.Called(ctx, complaint)
	return args.Error(0)
}

func (m *MockComplaintRepository) GetByKSUID(ctx context.Context, complaintKSUID string) (*model.Complaint, error) {
	args := m.Called(ctx, complaintKSUID)
	return args.Get(0).(*model.Complaint), args.Error(1)
}

func (m *MockComplaintRepository) GetByID(ctx context.Context, id uint) (*model.Complaint, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.Complaint), args.Error(1)
}

func (m *MockComplaintRepository) Update(ctx context.Context, complaint *model.Complaint) error {
	args := m.Called(ctx, complaint)
	return args.Error(0)
}

func (m *MockComplaintRepository) Delete(ctx context.Context, complaintKSUID string) error {
	args := m.Called(ctx, complaintKSUID)
	return args.Error(0)
}

func (m *MockComplaintRepository) GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.Complaint, int64, error) {
	args := m.Called(ctx, userKSUID, page, pageSize)
	return args.Get(0).([]*model.Complaint), args.Get(1).(int64), args.Error(2)
}

func (m *MockComplaintRepository) GetByStatus(ctx context.Context, status model.ComplaintStatus, page, pageSize int) ([]*model.Complaint, int64, error) {
	args := m.Called(ctx, status, page, pageSize)
	return args.Get(0).([]*model.Complaint), args.Get(1).(int64), args.Error(2)
}

func (m *MockComplaintRepository) GetByType(ctx context.Context, complaintType model.ComplaintType, page, pageSize int) ([]*model.Complaint, int64, error) {
	args := m.Called(ctx, complaintType, page, pageSize)
	return args.Get(0).([]*model.Complaint), args.Get(1).(int64), args.Error(2)
}

func (m *MockComplaintRepository) GetWithFilters(ctx context.Context, filters repository.ComplaintFilters) ([]*model.Complaint, int64, error) {
	args := m.Called(ctx, filters)
	return args.Get(0).([]*model.Complaint), args.Get(1).(int64), args.Error(2)
}

func (m *MockComplaintRepository) CountByUserKSUID(ctx context.Context, userKSUID string) (int64, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) CountByStatus(ctx context.Context, status model.ComplaintStatus) (int64, error) {
	args := m.Called(ctx, status)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) CountByType(ctx context.Context, complaintType model.ComplaintType) (int64, error) {
	args := m.Called(ctx, complaintType)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) GetStats(ctx context.Context, userKSUID string) (*repository.ComplaintStats, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(*repository.ComplaintStats), args.Error(1)
}

func (m *MockComplaintRepository) CountTodayComplaintsByUser(ctx context.Context, userKSUID string) (int64, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) CountByComplainerKSUID(ctx context.Context, userKSUID string) (int64, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) CountByComplainerKSUIDAndStatus(ctx context.Context, userKSUID string, status model.ComplaintStatus) (int64, error) {
	args := m.Called(ctx, userKSUID, status)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) CountByAccusedKSUID(ctx context.Context, userKSUID string) (int64, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) CountByContentKSUID(ctx context.Context, contentKSUID string) (int64, error) {
	args := m.Called(ctx, contentKSUID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) CountByContentKSUIDAndStatus(ctx context.Context, contentKSUID string, status model.ComplaintStatus) (int64, error) {
	args := m.Called(ctx, contentKSUID, status)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockComplaintRepository) GetByContentKSUIDAndStatus(ctx context.Context, contentKSUID string, status model.ComplaintStatus, page, pageSize int) ([]*model.Complaint, int64, error) {
	args := m.Called(ctx, contentKSUID, status, page, pageSize)
	return args.Get(0).([]*model.Complaint), args.Get(1).(int64), args.Error(2)
}

func (m *MockComplaintRepository) UpdateStatus(ctx context.Context, complaintKSUID string, status model.ComplaintStatus, processorKSUID, processNote, resolution string) error {
	args := m.Called(ctx, complaintKSUID, status, processorKSUID, processNote, resolution)
	return args.Error(0)
}

func (m *MockComplaintRepository) ExistsByContentKSUID(ctx context.Context, contentKSUID, userKSUID string) (bool, error) {
	args := m.Called(ctx, contentKSUID, userKSUID)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockComplaintRepository) GetPendingComplaints(ctx context.Context, page, pageSize int) ([]*model.Complaint, int64, error) {
	args := m.Called(ctx, page, pageSize)
	return args.Get(0).([]*model.Complaint), args.Get(1).(int64), args.Error(2)
}

// MockUserServiceClient 模拟用户服务客户端
type MockUserServiceClient struct {
	mock.Mock
}

func (m *MockUserServiceClient) BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) error {
	args := m.Called(ctx, blockerKSUID, blockedKSUID)
	return args.Error(0)
}

func (m *MockUserServiceClient) GetUserInfo(ctx context.Context, userKSUID string) (*service.UserInfo, error) {
	args := m.Called(ctx, userKSUID)
	return args.Get(0).(*service.UserInfo), args.Error(1)
}

// TestComplaintService_CreateComplaint 测试创建投诉
func TestComplaintService_CreateComplaint(t *testing.T) {
	// 创建模拟对象
	mockComplaintRepo := new(MockComplaintRepository)
	mockEvidenceRepo := new(MockComplaintRepository) // 简化，实际应该是EvidenceRepository
	mockViolationRepo := new(MockComplaintRepository) // 简化，实际应该是ViolationCategoryRepository
	mockUserClient := new(MockUserServiceClient)

	// 创建服务实例
	complaintService := service.NewComplaintService(
		mockComplaintRepo,
		mockEvidenceRepo,
		mockViolationRepo,
		mockUserClient,
		nil, // config
	)

	// 准备测试数据
	ctx := context.Background()
	req := &dto.CreateComplaintRequest{
		Type:        "piracy",
		Title:       "测试投诉",
		Description: "这是一个测试投诉",
		ContentKSUID: "test_content_ksuid",
		AccusedKSUID: "test_accused_ksuid",
		ViolationCategoryCode: "piracy",
	}

	// 设置模拟期望
	mockComplaintRepo.On("ExistsByContentKSUID", ctx, req.ContentKSUID, mock.AnythingOfType("string")).Return(false, nil)
	mockComplaintRepo.On("Create", ctx, mock.AnythingOfType("*model.Complaint")).Return(nil)

	// 执行测试
	response, err := complaintService.CreateComplaint(ctx, "test_user_ksuid", req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, req.Title, response.Title)
	assert.Equal(t, req.Description, response.Description)
	assert.Equal(t, model.ComplaintStatusPending, response.Status)

	// 验证模拟调用
	mockComplaintRepo.AssertExpectations(t)
}

// TestComplaintService_GetComplaint 测试获取投诉详情
func TestComplaintService_GetComplaint(t *testing.T) {
	// 创建模拟对象
	mockComplaintRepo := new(MockComplaintRepository)
	mockEvidenceRepo := new(MockComplaintRepository)
	mockViolationRepo := new(MockComplaintRepository)
	mockUserClient := new(MockUserServiceClient)

	// 创建服务实例
	complaintService := service.NewComplaintService(
		mockComplaintRepo,
		mockEvidenceRepo,
		mockViolationRepo,
		mockUserClient,
		nil,
	)

	// 准备测试数据
	ctx := context.Background()
	complaintKSUID := "test_complaint_ksuid"
	
	expectedComplaint := &model.Complaint{
		ComplaintKSUID:  complaintKSUID,
		ComplainerKSUID: "test_complainer_ksuid",
		AccusedKSUID:    "test_accused_ksuid",
		ContentKSUID:    "test_content_ksuid",
		Type:            model.ComplaintTypePiracy,
		Status:          model.ComplaintStatusPending,
		Title:           "测试投诉",
		Description:     "这是一个测试投诉",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 设置模拟期望
	mockComplaintRepo.On("GetByKSUID", ctx, complaintKSUID).Return(expectedComplaint, nil)

	// 执行测试
	response, err := complaintService.GetComplaint(ctx, complaintKSUID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, expectedComplaint.ComplaintKSUID, response.ComplaintKSUID)
	assert.Equal(t, expectedComplaint.Title, response.Title)
	assert.Equal(t, expectedComplaint.Status, response.Status)

	// 验证模拟调用
	mockComplaintRepo.AssertExpectations(t)
}
