package rights

import (
	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
)

// RegisterRightsRoutes 注册权益认证相关路由
func RegisterRightsRoutes(r *gin.RouterGroup, rightsHandler *handler.RightsHandler, authMiddleware gin.HandlerFunc) {
	// 权益认证路由组
	rightsGroup := r.Group("/rights")
	{
		// 权益认证管理路由（需要认证）
		verificationGroup := rightsGroup.Group("/verification")
		verificationGroup.Use(authMiddleware)
		{
			verificationGroup.POST("", rightsHandler.CreateRightsVerification)              // 创建权益认证
			verificationGroup.GET("/:rights_ksuid", rightsHandler.GetRightsVerification)    // 获取权益认证详情
			verificationGroup.GET("/my", rightsHandler.GetMyRightsVerifications)           // 获取我的权益认证列表
		}
	}
}
