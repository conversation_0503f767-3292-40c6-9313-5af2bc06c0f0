package dto

import (
	"time"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// ContentQueryRequest 内容查询请求DTO
type ContentQueryRequest struct {
	ContentTypes []string   `json:"content_types,omitempty" form:"content_types"`
	Status       string     `json:"status,omitempty" form:"status"`
	UserKSUID    string     `json:"user_ksuid,omitempty" form:"user_ksuid"`
	CategoryID   uint       `json:"category_id,omitempty" form:"category_id"`
	Tags         []string   `json:"tags,omitempty" form:"tags"`
	CreatedAfter *time.Time `json:"created_after,omitempty" form:"created_after"`
	CreatedBefore *time.Time `json:"created_before,omitempty" form:"created_before"`
	SortBy       string     `json:"sort_by,omitempty" form:"sort_by"`
	SortOrder    string     `json:"sort_order,omitempty" form:"sort_order"`
	Page         int        `json:"page,omitempty" form:"page"`
	Limit        int        `json:"limit,omitempty" form:"limit"`
	WithDetails  bool       `json:"with_details,omitempty" form:"with_details"`
}

// ContentResponse 内容响应DTO
type ContentResponse struct {
	ContentKSUID    string    `json:"content_ksuid"`
	ContentType     string    `json:"content_type"`
	Title          string    `json:"title"`
	Description    string    `json:"description"`
	UserKSUID      string    `json:"user_ksuid"`
	Status         string    `json:"status"`
	ViewCount      int64     `json:"view_count"`
	LikeCount      int64     `json:"like_count"`
	CommentCount   int64     `json:"comment_count"`
	FavoriteCount  int64     `json:"favorite_count"`
	CategoryID     uint      `json:"category_id"`
	CategoryName   string    `json:"category_name"`
	Tags           []string  `json:"tags"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	PublishedAt    *time.Time `json:"published_at,omitempty"`
	SourceService  string    `json:"source_service"`
	Details        interface{} `json:"details,omitempty"`
}

// ContentListResponse 内容列表响应DTO
type ContentListResponse struct {
	Contents   []*ContentResponse `json:"contents"`
	Total      int                `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
}

// UserContentQueryRequest 用户内容查询请求DTO
type UserContentQueryRequest struct {
	UserKSUID     string     `json:"user_ksuid" binding:"required"`
	ContentTypes  []string   `json:"content_types,omitempty"`
	Status        string     `json:"status,omitempty"`
	CategoryID    uint       `json:"category_id,omitempty"`
	Tags          []string   `json:"tags,omitempty"`
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
	SortBy        string     `json:"sort_by,omitempty"`
	SortOrder     string     `json:"sort_order,omitempty"`
	Page          int        `json:"page,omitempty"`
	Limit         int        `json:"limit,omitempty"`
	WithDetails   bool       `json:"with_details,omitempty"`
	IncludeDeleted bool      `json:"include_deleted,omitempty"`
}

// ContentUpdateRequest 内容更新请求DTO
type ContentUpdateRequest struct {
	Title       string   `json:"title,omitempty"`
	Description string   `json:"description,omitempty"`
	Status      string   `json:"status,omitempty"`
	CategoryID  uint     `json:"category_id,omitempty"`
	Tags        []string `json:"tags,omitempty"`
	Reason      string   `json:"reason,omitempty"`
}

// ContentStatsResponse 内容统计响应DTO
type ContentStatsResponse struct {
	ContentKSUID  string  `json:"content_ksuid"`
	ContentType   string  `json:"content_type"`
	ViewCount     int64   `json:"view_count"`
	LikeCount     int64   `json:"like_count"`
	CommentCount  int64   `json:"comment_count"`
	FavoriteCount int64   `json:"favorite_count"`
	ShareCount    int64   `json:"share_count,omitempty"`
	Duration      float64 `json:"duration,omitempty"`
	FileSize      int64   `json:"file_size,omitempty"`
}

// UserContentStatsResponse 用户内容统计响应DTO
type UserContentStatsResponse struct {
	UserKSUID      string                   `json:"user_ksuid"`
	TotalContents  int64                    `json:"total_contents"`
	ContentsByType map[string]int64         `json:"contents_by_type"`
	ContentsByStatus map[string]int64       `json:"contents_by_status"`
	TotalViews     int64                    `json:"total_views"`
	TotalLikes     int64                    `json:"total_likes"`
	TotalComments  int64                    `json:"total_comments"`
	TotalFavorites int64                    `json:"total_favorites"`
	FirstContentAt *time.Time               `json:"first_content_at,omitempty"`
	LastContentAt  *time.Time               `json:"last_content_at,omitempty"`
}

// ContentOverviewResponse 内容概览响应DTO
type ContentOverviewResponse struct {
	TotalContents    int64                    `json:"total_contents"`
	ContentsByType   map[string]int64         `json:"contents_by_type"`
	ContentsByStatus map[string]int64         `json:"contents_by_status"`
	RecentContents   int64                    `json:"recent_contents"`
	ActiveUsers      int64                    `json:"active_users"`
	TotalViews       int64                    `json:"total_views"`
	TotalLikes       int64                    `json:"total_likes"`
	TotalComments    int64                    `json:"total_comments"`
	TotalFavorites   int64                    `json:"total_favorites"`
}

// ContentTrendsResponse 内容趋势响应DTO
type ContentTrendsResponse struct {
	Period        string      `json:"period"`
	ContentCounts []TrendData `json:"content_counts"`
	ViewCounts    []TrendData `json:"view_counts"`
	LikeCounts    []TrendData `json:"like_counts"`
	CommentCounts []TrendData `json:"comment_counts"`
}

// TrendData 趋势数据DTO
type TrendData struct {
	Date  string `json:"date"`
	Value int64  `json:"value"`
}

// CategoryStatsResponse 分类统计响应DTO
type CategoryStatsResponse struct {
	CategoryID    uint   `json:"category_id"`
	CategoryName  string `json:"category_name"`
	ContentCount  int64  `json:"content_count"`
	TotalViews    int64  `json:"total_views"`
	TotalLikes    int64  `json:"total_likes"`
	TotalComments int64  `json:"total_comments"`
}

// TagStatsResponse 标签统计响应DTO
type TagStatsResponse struct {
	TagName         string  `json:"tag_name"`
	ContentCount    int64   `json:"content_count"`
	TotalViews      int64   `json:"total_views"`
	TotalLikes      int64   `json:"total_likes"`
	PopularityScore float64 `json:"popularity_score"`
}

// 转换函数

// ToContentFilters 转换为内容过滤器
func (req *ContentQueryRequest) ToContentFilters() *types.ContentFilters {
	return &types.ContentFilters{
		ContentTypes:  req.ContentTypes,
		Status:        req.Status,
		UserKSUID:     req.UserKSUID,
		CategoryID:    req.CategoryID,
		Tags:          req.Tags,
		CreatedAfter:  req.CreatedAfter,
		CreatedBefore: req.CreatedBefore,
		SortBy:        req.SortBy,
		SortOrder:     req.SortOrder,
		Page:          req.Page,
		Limit:         req.Limit,
	}
}

// ToUserContentFilters 转换为用户内容过滤器
func (req *UserContentQueryRequest) ToUserContentFilters() *types.UserContentFilters {
	return &types.UserContentFilters{
		ContentFilters: types.ContentFilters{
			ContentTypes:  req.ContentTypes,
			Status:        req.Status,
			UserKSUID:     req.UserKSUID,
			CategoryID:    req.CategoryID,
			Tags:          req.Tags,
			CreatedAfter:  req.CreatedAfter,
			CreatedBefore: req.CreatedBefore,
			SortBy:        req.SortBy,
			SortOrder:     req.SortOrder,
			Page:          req.Page,
			Limit:         req.Limit,
		},
		IncludeDeleted: req.IncludeDeleted,
	}
}

// ToContentUpdateRequest 转换为内容更新请求
func (req *ContentUpdateRequest) ToContentUpdateRequest() *types.ContentUpdateRequest {
	return &types.ContentUpdateRequest{
		Title:       req.Title,
		Description: req.Description,
		Status:      req.Status,
		CategoryID:  req.CategoryID,
		Tags:        req.Tags,
	}
}

// FromBaseContent 从基础内容转换
func FromBaseContent(content *types.BaseContent) *ContentResponse {
	if content == nil {
		return nil
	}

	return &ContentResponse{
		ContentKSUID:  content.ContentKSUID,
		ContentType:   content.ContentType,
		Title:         content.Title,
		Description:   content.Description,
		UserKSUID:     content.UserKSUID,
		Status:        content.Status,
		ViewCount:     content.ViewCount,
		LikeCount:     content.LikeCount,
		CommentCount:  content.CommentCount,
		FavoriteCount: content.FavoriteCount,
		CategoryID:    content.CategoryID,
		CategoryName:  content.CategoryName,
		Tags:          content.Tags,
		CreatedAt:     content.CreatedAt,
		UpdatedAt:     content.UpdatedAt,
		PublishedAt:   content.PublishedAt,
		SourceService: content.SourceService,
	}
}

// FromContentWithDetails 从详细内容转换
func FromContentWithDetails(content *types.ContentWithDetails) *ContentResponse {
	if content == nil {
		return nil
	}

	response := FromBaseContent(&content.BaseContent)
	if response != nil {
		response.Details = content.Details
	}
	return response
}

// FromBaseContentList 从基础内容列表转换
func FromBaseContentList(contentList *types.BaseContentList) *ContentListResponse {
	if contentList == nil {
		return &ContentListResponse{
			Contents: []*ContentResponse{},
		}
	}

	contents := make([]*ContentResponse, len(contentList.Contents))
	for i, content := range contentList.Contents {
		contents[i] = FromBaseContent(content)
	}

	return &ContentListResponse{
		Contents:   contents,
		Total:      contentList.Total,
		Page:       contentList.Page,
		Limit:      contentList.Limit,
		TotalPages: contentList.TotalPages,
	}
}

// FromContentWithDetailsList 从详细内容列表转换
func FromContentWithDetailsList(contentList *types.ContentWithDetailsList) *ContentListResponse {
	if contentList == nil {
		return &ContentListResponse{
			Contents: []*ContentResponse{},
		}
	}

	contents := make([]*ContentResponse, len(contentList.Contents))
	for i, content := range contentList.Contents {
		contents[i] = FromContentWithDetails(content)
	}

	return &ContentListResponse{
		Contents:   contents,
		Total:      contentList.Total,
		Page:       contentList.Page,
		Limit:      contentList.Limit,
		TotalPages: contentList.TotalPages,
	}
}

// FromContentStatsOverview 从内容统计概览转换
func FromContentStatsOverview(stats *types.ContentStatsOverview) *ContentOverviewResponse {
	if stats == nil {
		return nil
	}

	return &ContentOverviewResponse{
		TotalContents:    stats.TotalContents,
		ContentsByType:   stats.ContentsByType,
		ContentsByStatus: stats.ContentsByStatus,
		RecentContents:   stats.RecentContents,
		ActiveUsers:      stats.ActiveUsers,
		TotalViews:       stats.TotalViews,
		TotalLikes:       stats.TotalLikes,
		TotalComments:    stats.TotalComments,
		TotalFavorites:   stats.TotalFavorites,
	}
}

// FromUserContentStats 从用户内容统计转换
func FromUserContentStats(stats *types.UserContentStats) *UserContentStatsResponse {
	if stats == nil {
		return nil
	}

	return &UserContentStatsResponse{
		UserKSUID:        stats.UserKSUID,
		TotalContents:    stats.TotalContents,
		ContentsByType:   stats.ContentsByType,
		ContentsByStatus: stats.ContentsByStatus,
		TotalViews:       stats.TotalViews,
		TotalLikes:       stats.TotalLikes,
		TotalComments:    stats.TotalComments,
		TotalFavorites:   stats.TotalFavorites,
		FirstContentAt:   stats.FirstContentAt,
		LastContentAt:    stats.LastContentAt,
	}
}
