package utils

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// DataAggregator 数据聚合器接口
type DataAggregator interface {
	// 内容聚合
	AggregateContentWithInteraction(ctx context.Context, contentKSUID string, contentType string) (*types.BaseContent, error)
	BatchAggregateContentWithInteraction(ctx context.Context, contentKSUIDs []string, contentType string) ([]*types.BaseContent, error)

	// 用户内容聚合
	AggregateUserContent(ctx context.Context, userKSUID string, contentTypes []string, filters *types.ContentFilters) (*types.BaseContentList, error)

	// 统计聚合
	AggregateContentStats(ctx context.Context, contentKSUID string, contentType string) (*types.ContentStatsOverview, error)
	AggregateUserStats(ctx context.Context, userKSUID string) (*types.UserContentStats, error)
	AggregateOverallStats(ctx context.Context) (*types.ContentStatsOverview, error)

	// 跨服务数据聚合
	AggregateMultiServiceContent(ctx context.Context, filters *types.ContentFilters) (*types.BaseContentList, error)
}

// dataAggregator 数据聚合器实现
type dataAggregator struct {
	videoClient       client.VideoServiceClient
	interactionClient client.InteractionServiceClient
	converter         ContentConverter
}

// NewDataAggregator 创建数据聚合器
func NewDataAggregator(videoClient client.VideoServiceClient, interactionClient client.InteractionServiceClient) DataAggregator {
	return &dataAggregator{
		videoClient:       videoClient,
		interactionClient: interactionClient,
		converter:         NewContentConverter(),
	}
}

// AggregateContentWithInteraction 聚合内容和交互数据
func (a *dataAggregator) AggregateContentWithInteraction(ctx context.Context, contentKSUID string, contentType string) (*types.BaseContent, error) {
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("content_type", contentType).
		Msg("开始聚合内容和交互数据")

	switch contentType {
	case "video":
		return a.aggregateVideoWithInteraction(ctx, contentKSUID)
	case "novel":
		// TODO: 实现小说聚合
		return nil, fmt.Errorf("novel content aggregation not implemented yet")
	case "music":
		// TODO: 实现音乐聚合
		return nil, fmt.Errorf("music content aggregation not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported content type: %s", contentType)
	}
}

// aggregateVideoWithInteraction 聚合视频和交互数据
func (a *dataAggregator) aggregateVideoWithInteraction(ctx context.Context, contentKSUID string) (*types.BaseContent, error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	var video *client.VideoContent
	var interaction *types.InteractionStats
	var errors []error

	// 并发获取视频内容和交互统计
	wg.Add(2)

	// 获取视频内容
	go func() {
		defer wg.Done()
		v, err := a.videoClient.GetContentByKSUID(contentKSUID)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to get video content: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		video = v
		mu.Unlock()
	}()

	// 获取交互统计
	go func() {
		defer wg.Done()
		i, err := a.interactionClient.GetContentInteractionStats(contentKSUID)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to get interaction stats: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		interaction = i
		mu.Unlock()
	}()

	wg.Wait()

	// 检查错误
	if len(errors) > 0 {
		log.Error().
			Str("content_ksuid", contentKSUID).
			Interface("errors", errors).
			Msg("聚合视频和交互数据时发生错误")
		return nil, fmt.Errorf("aggregation errors: %v", errors)
	}

	if video == nil {
		return nil, fmt.Errorf("video content not found")
	}

	// 转换为基础内容
	baseContent := a.converter.ConvertVideoToBaseWithInteraction(video, interaction)

	log.Debug().
		Str("content_ksuid", contentKSUID).
		Str("title", baseContent.Title).
		Int64("like_count", baseContent.LikeCount).
		Msg("聚合视频和交互数据成功")

	return baseContent, nil
}

// BatchAggregateContentWithInteraction 批量聚合内容和交互数据
func (a *dataAggregator) BatchAggregateContentWithInteraction(ctx context.Context, contentKSUIDs []string, contentType string) ([]*types.BaseContent, error) {
	if len(contentKSUIDs) == 0 {
		return []*types.BaseContent{}, nil
	}

	log.Debug().
		Int("count", len(contentKSUIDs)).
		Str("content_type", contentType).
		Msg("开始批量聚合内容和交互数据")

	switch contentType {
	case "video":
		return a.batchAggregateVideoWithInteraction(ctx, contentKSUIDs)
	case "novel":
		// TODO: 实现小说批量聚合
		return nil, fmt.Errorf("novel content batch aggregation not implemented yet")
	case "music":
		// TODO: 实现音乐批量聚合
		return nil, fmt.Errorf("music content batch aggregation not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported content type: %s", contentType)
	}
}

// batchAggregateVideoWithInteraction 批量聚合视频和交互数据
func (a *dataAggregator) batchAggregateVideoWithInteraction(ctx context.Context, contentKSUIDs []string) ([]*types.BaseContent, error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	var videos map[string]*client.VideoContent
	var interactions map[string]*types.InteractionStats
	var errors []error

	// 并发获取视频内容和交互统计
	wg.Add(2)

	// 批量获取视频内容
	go func() {
		defer wg.Done()
		v, err := a.videoClient.BatchGetContentsByKSUIDs(contentKSUIDs)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to batch get video contents: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		videos = v
		mu.Unlock()
	}()

	// 批量获取交互统计
	go func() {
		defer wg.Done()
		i, err := a.interactionClient.BatchGetContentInteractionStats(contentKSUIDs)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to batch get interaction stats: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		interactions = i
		mu.Unlock()
	}()

	wg.Wait()

	// 检查错误
	if len(errors) > 0 {
		log.Error().
			Int("count", len(contentKSUIDs)).
			Interface("errors", errors).
			Msg("批量聚合视频和交互数据时发生错误")
		return nil, fmt.Errorf("batch aggregation errors: %v", errors)
	}

	// 转换为基础内容列表
	var videoList []*client.VideoContent
	for _, contentKSUID := range contentKSUIDs {
		if video, exists := videos[contentKSUID]; exists {
			videoList = append(videoList, video)
		}
	}

	baseContents := a.converter.BatchConvertVideosToBaseWithInteraction(videoList, interactions)

	log.Debug().
		Int("input_count", len(contentKSUIDs)).
		Int("video_count", len(videoList)).
		Int("output_count", len(baseContents)).
		Msg("批量聚合视频和交互数据成功")

	return baseContents, nil
}

// AggregateUserContent 聚合用户内容
func (a *dataAggregator) AggregateUserContent(ctx context.Context, userKSUID string, contentTypes []string, filters *types.ContentFilters) (*types.BaseContentList, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Interface("content_types", contentTypes).
		Msg("开始聚合用户内容")

	// 如果没有指定内容类型，默认获取所有支持的类型
	if len(contentTypes) == 0 {
		contentTypes = []string{"video"} // 目前只支持视频
	}

	var allContents []*types.BaseContent
	var totalCount int

	for _, contentType := range contentTypes {
		switch contentType {
		case "video":
			contents, err := a.aggregateUserVideoContent(ctx, userKSUID, filters)
			if err != nil {
				log.Error().
					Err(err).
					Str("user_ksuid", userKSUID).
					Str("content_type", contentType).
					Msg("聚合用户视频内容失败")
				continue
			}
			allContents = append(allContents, contents.Contents...)
			totalCount += contents.Total
		case "novel":
			// TODO: 实现小说用户内容聚合
			log.Debug().Msg("小说用户内容聚合尚未实现")
		case "music":
			// TODO: 实现音乐用户内容聚合
			log.Debug().Msg("音乐用户内容聚合尚未实现")
		}
	}

	// 按创建时间排序
	// TODO: 实现排序逻辑

	result := &types.BaseContentList{
		Contents:   allContents,
		Total:      totalCount,
		Page:       1,
		Limit:      len(allContents),
		TotalPages: 1,
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("total_contents", len(allContents)).
		Msg("聚合用户内容成功")

	return result, nil
}

// aggregateUserVideoContent 聚合用户视频内容
func (a *dataAggregator) aggregateUserVideoContent(ctx context.Context, userKSUID string, filters *types.ContentFilters) (*types.BaseContentList, error) {
	// 获取用户视频内容
	videoList, err := a.videoClient.GetUserContents(userKSUID, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get user video contents: %w", err)
	}

	if len(videoList.Contents) == 0 {
		return &types.BaseContentList{
			Contents: []*types.BaseContent{},
		}, nil
	}

	// 获取内容KSUIDs
	contentKSUIDs := make([]string, len(videoList.Contents))
	for i, video := range videoList.Contents {
		contentKSUIDs[i] = video.ContentKSUID
	}

	// 批量获取交互统计
	interactions, err := a.interactionClient.BatchGetContentInteractionStats(contentKSUIDs)
	if err != nil {
		log.Warn().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取交互统计失败，将使用默认值")
		interactions = make(map[string]*types.InteractionStats)
	}

	// 转换为基础内容列表
	return ConvertVideoListToBaseList(videoList, interactions), nil
}
