package dto

import (
	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/pkg/errors"
	"time"
)

// CreateComplaintRequest 创建投诉请求
type CreateComplaintRequest struct {
	ContentKSUID     string                `json:"content_ksuid" binding:"required"`     // 被投诉内容KSUID
	ContentType      string                `json:"content_type" binding:"required"`      // 被投诉内容类型
	Type             model.ComplaintType   `json:"type" binding:"required"`              // 投诉类型
	Title            string                `json:"title" binding:"required"`             // 投诉标题
	Description      string                `json:"description"`                          // 详细描述
	UserRole         model.UserRole        `json:"user_role"`                            // 用户角色(盗版投诉使用)
	OriginalURL      string                `json:"original_url"`                         // 原视频出处(盗版投诉使用)
	WorkURL          string                `json:"work_url"`                             // 本站作品地址(盗版投诉使用)
	ViolationType    string                `json:"violation_type"`                       // 违规类型(稿件投诉使用)
	ViolationSubType string                `json:"violation_sub_type"`                   // 违规子类型(稿件投诉使用)
	BlockUserKSUID   string                `json:"block_user_ksuid"`                     // 需要拉黑的用户KSUID(盗版投诉使用)
}

// UpdateComplaintRequest 更新投诉请求
type UpdateComplaintRequest struct {
	Title            string `json:"title"`             // 投诉标题
	Description      string `json:"description"`       // 详细描述
	OriginalURL      string `json:"original_url"`      // 原视频出处
	WorkURL          string `json:"work_url"`          // 本站作品地址
	ViolationType    string `json:"violation_type"`    // 违规类型
	ViolationSubType string `json:"violation_sub_type"` // 违规子类型
}

// ProcessComplaintRequest 处理投诉请求
type ProcessComplaintRequest struct {
	Status      model.ComplaintStatus `json:"status" binding:"required"`      // 处理状态
	ProcessNote string                `json:"process_note"`                   // 处理备注
	Resolution  string                `json:"resolution"`                     // 处理结果
}

// GetComplaintsRequest 获取投诉列表请求
type GetComplaintsRequest struct {
	Page         int                   `form:"page" binding:"min=1"`           // 页码
	PageSize     int                   `form:"page_size" binding:"min=1,max=50"` // 每页数量
	Type         model.ComplaintType   `form:"type"`                           // 投诉类型过滤
	Status       model.ComplaintStatus `form:"status"`                         // 状态过滤
	ContentType  string                `form:"content_type"`                   // 内容类型过滤
	StartDate    string                `form:"start_date"`                     // 开始日期(YYYY-MM-DD)
	EndDate      string                `form:"end_date"`                       // 结束日期(YYYY-MM-DD)
	SortBy       string                `form:"sort_by"`                        // 排序字段
	SortOrder    string                `form:"sort_order"`                     // 排序方向(asc/desc)
}

// GetMyComplaintsRequest 获取我的投诉列表请求
type GetMyComplaintsRequest struct {
	Page      int                   `form:"page" binding:"min=1"`             // 页码
	PageSize  int                   `form:"page_size" binding:"min=1,max=50"` // 每页数量
	Type      model.ComplaintType   `form:"type"`                             // 投诉类型过滤
	Status    model.ComplaintStatus `form:"status"`                           // 状态过滤
	StartDate string                `form:"start_date"`                       // 开始日期
	EndDate   string                `form:"end_date"`                         // 结束日期
}

// GetComplaintsAgainstMeRequest 获取针对我的投诉列表请求
type GetComplaintsAgainstMeRequest struct {
	Page      int                   `form:"page" binding:"min=1"`             // 页码
	PageSize  int                   `form:"page_size" binding:"min=1,max=50"` // 每页数量
	Status    model.ComplaintStatus `form:"status"`                           // 状态过滤
	StartDate string                `form:"start_date"`                       // 开始日期
	EndDate   string                `form:"end_date"`                         // 结束日期
}

// ComplaintResponse 投诉响应
type ComplaintResponse struct {
	ComplaintKSUID   string                `json:"complaint_ksuid"`   // 投诉KSUID
	UserKSUID        string                `json:"user_ksuid"`        // 投诉人KSUID
	ContentKSUID     string                `json:"content_ksuid"`     // 被投诉内容KSUID
	ContentType      string                `json:"content_type"`      // 被投诉内容类型
	Type             model.ComplaintType   `json:"type"`              // 投诉类型
	Status           model.ComplaintStatus `json:"status"`            // 投诉状态
	Title            string                `json:"title"`             // 投诉标题
	Description      string                `json:"description"`       // 详细描述
	UserRole         model.UserRole        `json:"user_role"`         // 用户角色
	OriginalURL      string                `json:"original_url"`      // 原视频出处
	WorkURL          string                `json:"work_url"`          // 本站作品地址
	ViolationType    string                `json:"violation_type"`    // 违规类型
	ViolationSubType string                `json:"violation_sub_type"` // 违规子类型
	EvidenceFiles    []EvidenceFileResponse `json:"evidence_files"`    // 证据文件
	ProcessorKSUID   string                `json:"processor_ksuid"`   // 处理人KSUID
	ProcessedAt      *time.Time            `json:"processed_at"`      // 处理时间
	ProcessNote      string                `json:"process_note"`      // 处理备注
	Resolution       string                `json:"resolution"`        // 处理结果
	CreatedAt        time.Time             `json:"created_at"`        // 创建时间
	UpdatedAt        time.Time             `json:"updated_at"`        // 更新时间
}

// EvidenceFileResponse 证据文件响应
type EvidenceFileResponse struct {
	ID          uint   `json:"id"`           // 文件ID
	FileName    string `json:"file_name"`    // 文件名
	FileURL     string `json:"file_url"`     // 文件URL
	FileSize    int64  `json:"file_size"`    // 文件大小
	ContentType string `json:"content_type"` // 文件类型
	CreatedAt   time.Time `json:"created_at"` // 上传时间
}

// ComplaintListResponse 投诉列表响应
type ComplaintListResponse struct {
	Complaints []ComplaintResponse `json:"complaints"`   // 投诉列表
	Total      int64               `json:"total"`        // 总数
	Page       int                 `json:"page"`         // 当前页码
	PageSize   int                 `json:"page_size"`    // 每页数量
	TotalPages int                 `json:"total_pages"`  // 总页数
}

// ComplaintStatsResponse 投诉统计响应
type ComplaintStatsResponse struct {
	TotalComplaints     int64 `json:"total_complaints"`     // 总投诉数
	PendingComplaints   int64 `json:"pending_complaints"`   // 待处理投诉数
	CompletedComplaints int64 `json:"completed_complaints"` // 已完成投诉数
	CancelledComplaints int64 `json:"cancelled_complaints"` // 已取消投诉数
	MyComplaints        int64 `json:"my_complaints"`        // 我的投诉数
	ComplaintsAgainstMe int64 `json:"complaints_against_me"` // 针对我的投诉数
}

// UploadEvidenceRequest 上传证据请求
type UploadEvidenceRequest struct {
	ComplaintKSUID string `form:"complaint_ksuid" binding:"required"` // 投诉KSUID
}

// UploadEvidenceResponse 上传证据响应
type UploadEvidenceResponse struct {
	Files []EvidenceFileResponse `json:"files"` // 上传的文件列表
}

// DeleteEvidenceRequest 删除证据请求
type DeleteEvidenceRequest struct {
	EvidenceID uint `json:"evidence_id" binding:"required"` // 证据文件ID
}

// ViolationCategoryResponse 违规类别响应
type ViolationCategoryResponse struct {
	Code        string                      `json:"code"`        // 类别代码
	Name        string                      `json:"name"`        // 类别名称
	Description string                      `json:"description"` // 类别描述
	ParentCode  string                      `json:"parent_code"` // 父类别代码
	SortOrder   int                         `json:"sort_order"`  // 排序
	Children    []ViolationCategoryResponse `json:"children"`    // 子类别
}

// 验证函数
func ValidateCreateComplaintRequest(req *CreateComplaintRequest) *errors.Errors {
	if req.ContentKSUID == "" {
		return errors.NewValidationError("被投诉内容KSUID不能为空")
	}
	if req.ContentType == "" {
		return errors.NewValidationError("被投诉内容类型不能为空")
	}
	if req.Type == "" {
		return errors.NewValidationError("投诉类型不能为空")
	}
	if req.Title == "" {
		return errors.NewValidationError("投诉标题不能为空")
	}
	if len(req.Title) > 255 {
		return errors.NewValidationError("投诉标题长度不能超过255个字符")
	}
	if len(req.Description) > 5000 {
		return errors.NewValidationError("投诉描述长度不能超过5000个字符")
	}

	// 根据投诉类型验证特定字段
	switch req.Type {
	case model.ComplaintTypePiracy:
		if req.UserRole == "" {
			return errors.NewValidationError("盗版投诉必须指定用户角色")
		}
		if req.OriginalURL == "" {
			return errors.NewValidationError("盗版投诉必须提供原视频出处")
		}
	case model.ComplaintTypeContent:
		if req.ViolationType == "" {
			return errors.NewValidationError("稿件投诉必须指定违规类型")
		}
	}

	return nil
}

// 转换函数
func ComplaintToResponse(complaint *model.Complaint) *ComplaintResponse {
	resp := &ComplaintResponse{
		ComplaintKSUID:   complaint.ComplaintKSUID,
		UserKSUID:        complaint.UserKSUID,
		ContentKSUID:     complaint.ContentKSUID,
		ContentType:      complaint.ContentType,
		Type:             complaint.Type,
		Status:           complaint.Status,
		Title:            complaint.Title,
		Description:      complaint.Description,
		UserRole:         complaint.UserRole,
		OriginalURL:      complaint.OriginalURL,
		WorkURL:          complaint.WorkURL,
		ViolationType:    complaint.ViolationType,
		ViolationSubType: complaint.ViolationSubType,
		ProcessorKSUID:   complaint.ProcessorKSUID,
		ProcessedAt:      complaint.ProcessedAt,
		ProcessNote:      complaint.ProcessNote,
		Resolution:       complaint.Resolution,
		CreatedAt:        complaint.CreatedAt,
		UpdatedAt:        complaint.UpdatedAt,
	}

	// 转换证据文件
	for _, evidence := range complaint.EvidenceFiles {
		resp.EvidenceFiles = append(resp.EvidenceFiles, EvidenceFileResponse{
			ID:          evidence.ID,
			FileName:    evidence.FileName,
			FileURL:     evidence.FileURL,
			FileSize:    evidence.FileSize,
			ContentType: evidence.ContentType,
			CreatedAt:   evidence.CreatedAt,
		})
	}

	return resp
}

// ProcessComplaintRequest 处理投诉请求
type ProcessComplaintRequest struct {
	Status      model.ComplaintStatus `json:"status" binding:"required" example:"approved"`        // 处理状态
	ProcessNote string                `json:"process_note" binding:"required" example:"投诉成立"`    // 处理说明
	Resolution  string                `json:"resolution" example:"内容已删除"`                        // 处理结果
}

// ComplaintFilters 投诉过滤条件
type ComplaintFilters struct {
	Page           int                   `json:"page" example:"1"`
	PageSize       int                   `json:"page_size" example:"20"`
	Status         model.ComplaintStatus `json:"status,omitempty" example:"pending"`
	Type           model.ComplaintType   `json:"type,omitempty" example:"piracy"`
	ComplainerKSUID string               `json:"complainer_ksuid,omitempty" example:"user123"`
	AccusedKSUID   string                `json:"accused_ksuid,omitempty" example:"user456"`
	ContentKSUID   string                `json:"content_ksuid,omitempty" example:"content789"`
	StartDate      string                `json:"start_date,omitempty" example:"2024-01-01"`
	EndDate        string                `json:"end_date,omitempty" example:"2024-12-31"`
	SortBy         string                `json:"sort_by,omitempty" example:"created_at"`
	SortOrder      string                `json:"sort_order,omitempty" example:"desc"`
}
