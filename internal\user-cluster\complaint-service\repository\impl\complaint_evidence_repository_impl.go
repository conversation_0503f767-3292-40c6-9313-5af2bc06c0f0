package impl

import (
	"context"
	"errors"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/internal/user-cluster/complaint-service/repository"
	"pxpat-backend/pkg/cache"
)

// complaintEvidenceRepo 投诉证据文件数据访问层实现
type complaintEvidenceRepo struct {
	db          *gorm.DB
	rdb         *redis.Client
	cacheManage cache.Manager
}

// NewComplaintEvidenceRepository 创建投诉证据文件数据访问层实例
func NewComplaintEvidenceRepository(db *gorm.DB, rdb *redis.Client, cacheManage cache.Manager) repository.ComplaintEvidenceRepository {
	return &complaintEvidenceRepo{
		db:          db,
		rdb:         rdb,
		cacheManage: cacheManage,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *complaintEvidenceRepo) GetDB() interface{} {
	return r.db
}

// Create 创建证据文件记录
func (r *complaintEvidenceRepo) Create(ctx context.Context, evidence *model.ComplaintEvidence) error {
	log.Debug().
		Str("complaint_ksuid", evidence.ComplaintKSUID).
		Str("file_name", evidence.FileName).
		Msg("开始创建投诉证据文件记录")

	err := r.db.WithContext(ctx).Create(evidence).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", evidence.ComplaintKSUID).
			Str("file_name", evidence.FileName).
			Msg("创建投诉证据文件记录失败")
		return err
	}

	log.Debug().
		Str("complaint_ksuid", evidence.ComplaintKSUID).
		Str("file_name", evidence.FileName).
		Msg("创建投诉证据文件记录成功")

	return nil
}

// GetByID 根据ID获取证据文件
func (r *complaintEvidenceRepo) GetByID(ctx context.Context, id uint) (*model.ComplaintEvidence, error) {
	var evidence model.ComplaintEvidence
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&evidence).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrComplaintEvidenceNotFound
		}
		return nil, err
	}

	return &evidence, nil
}

// GetByComplaintKSUID 根据投诉KSUID获取证据文件列表
func (r *complaintEvidenceRepo) GetByComplaintKSUID(ctx context.Context, complaintKSUID string) ([]*model.ComplaintEvidence, error) {
	var evidences []*model.ComplaintEvidence
	err := r.db.WithContext(ctx).
		Where("complaint_ksuid = ?", complaintKSUID).
		Order("created_at ASC").
		Find(&evidences).Error

	if err != nil {
		return nil, err
	}

	return evidences, nil
}

// Update 更新证据文件记录
func (r *complaintEvidenceRepo) Update(ctx context.Context, evidence *model.ComplaintEvidence) error {
	log.Debug().
		Uint("evidence_id", evidence.ID).
		Str("complaint_ksuid", evidence.ComplaintKSUID).
		Msg("开始更新投诉证据文件记录")

	err := r.db.WithContext(ctx).Save(evidence).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint("evidence_id", evidence.ID).
			Msg("更新投诉证据文件记录失败")
		return err
	}

	log.Debug().
		Uint("evidence_id", evidence.ID).
		Msg("更新投诉证据文件记录成功")

	return nil
}

// Delete 删除证据文件记录
func (r *complaintEvidenceRepo) Delete(ctx context.Context, id uint) error {
	log.Debug().
		Uint("evidence_id", id).
		Msg("开始删除投诉证据文件记录")

	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		Delete(&model.ComplaintEvidence{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Uint("evidence_id", id).
			Msg("删除投诉证据文件记录失败")
		return err
	}

	log.Debug().
		Uint("evidence_id", id).
		Msg("删除投诉证据文件记录成功")

	return nil
}

// DeleteByComplaintKSUID 根据投诉KSUID删除所有证据文件
func (r *complaintEvidenceRepo) DeleteByComplaintKSUID(ctx context.Context, complaintKSUID string) error {
	log.Debug().
		Str("complaint_ksuid", complaintKSUID).
		Msg("开始删除投诉的所有证据文件记录")

	err := r.db.WithContext(ctx).
		Where("complaint_ksuid = ?", complaintKSUID).
		Delete(&model.ComplaintEvidence{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("complaint_ksuid", complaintKSUID).
			Msg("删除投诉的所有证据文件记录失败")
		return err
	}

	log.Debug().
		Str("complaint_ksuid", complaintKSUID).
		Msg("删除投诉的所有证据文件记录成功")

	return nil
}

// BatchCreate 批量创建证据文件记录
func (r *complaintEvidenceRepo) BatchCreate(ctx context.Context, evidences []*model.ComplaintEvidence) error {
	if len(evidences) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(evidences)).
		Msg("开始批量创建投诉证据文件记录")

	err := r.db.WithContext(ctx).CreateInBatches(evidences, 100).Error
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(evidences)).
			Msg("批量创建投诉证据文件记录失败")
		return err
	}

	log.Debug().
		Int("count", len(evidences)).
		Msg("批量创建投诉证据文件记录成功")

	return nil
}

// BatchDelete 批量删除证据文件记录
func (r *complaintEvidenceRepo) BatchDelete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(ids)).
		Msg("开始批量删除投诉证据文件记录")

	err := r.db.WithContext(ctx).
		Where("id IN ?", ids).
		Delete(&model.ComplaintEvidence{}).Error

	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(ids)).
			Msg("批量删除投诉证据文件记录失败")
		return err
	}

	log.Debug().
		Int("count", len(ids)).
		Msg("批量删除投诉证据文件记录成功")

	return nil
}

// CountByComplaintKSUID 统计投诉的证据文件数量
func (r *complaintEvidenceRepo) CountByComplaintKSUID(ctx context.Context, complaintKSUID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ComplaintEvidence{}).
		Where("complaint_ksuid = ?", complaintKSUID).
		Count(&count).Error
	return count, err
}

// GetTotalSizeByComplaintKSUID 获取投诉的证据文件总大小
func (r *complaintEvidenceRepo) GetTotalSizeByComplaintKSUID(ctx context.Context, complaintKSUID string) (int64, error) {
	var totalSize int64
	err := r.db.WithContext(ctx).
		Model(&model.ComplaintEvidence{}).
		Where("complaint_ksuid = ?", complaintKSUID).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&totalSize).Error
	return totalSize, err
}
