package dto

import (
	"time"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// StatsQueryRequest 统计查询请求DTO
type StatsQueryRequest struct {
	ContentType string     `json:"content_type,omitempty" form:"content_type"`
	UserKSUID   string     `json:"user_ksuid,omitempty" form:"user_ksuid"`
	CategoryID  uint       `json:"category_id,omitempty" form:"category_id"`
	StartTime   *time.Time `json:"start_time,omitempty" form:"start_time"`
	EndTime     *time.Time `json:"end_time,omitempty" form:"end_time"`
	Period      string     `json:"period,omitempty" form:"period"` // daily, weekly, monthly
}

// ContentTypeStatsResponse 内容类型统计响应DTO
type ContentTypeStatsResponse struct {
	ContentType     string  `json:"content_type"`
	TotalCount      int64   `json:"total_count"`
	PublishedCount  int64   `json:"published_count"`
	DraftCount      int64   `json:"draft_count"`
	ArchivedCount   int64   `json:"archived_count"`
	DeletedCount    int64   `json:"deleted_count"`
	TotalViews      int64   `json:"total_views"`
	TotalLikes      int64   `json:"total_likes"`
	TotalComments   int64   `json:"total_comments"`
	TotalFavorites  int64   `json:"total_favorites"`
	AvgViews        float64 `json:"avg_views"`
	AvgLikes        float64 `json:"avg_likes"`
}

// InteractionStatsResponse 交互统计响应DTO
type InteractionStatsResponse struct {
	ContentKSUID  string `json:"content_ksuid"`
	LikeCount     int64  `json:"like_count"`
	DislikeCount  int64  `json:"dislike_count"`
	FavoriteCount int64  `json:"favorite_count"`
}

// UserInteractionStatsResponse 用户交互统计响应DTO
type UserInteractionStatsResponse struct {
	UserKSUID              string `json:"user_ksuid"`
	TotalLikesGiven        int64  `json:"total_likes_given"`
	TotalLikesReceived     int64  `json:"total_likes_received"`
	TotalFavoritesGiven    int64  `json:"total_favorites_given"`
	TotalFavoritesReceived int64  `json:"total_favorites_received"`
}

// OverallInteractionStatsResponse 总体交互统计响应DTO
type OverallInteractionStatsResponse struct {
	TotalLikes     int64 `json:"total_likes"`
	TotalDislikes  int64 `json:"total_dislikes"`
	TotalFavorites int64 `json:"total_favorites"`
}

// ContentTrendData 内容趋势数据DTO
type ContentTrendData struct {
	Date  string `json:"date"`
	Value int64  `json:"value"`
}

// ContentTrendsStatsResponse 内容趋势统计响应DTO
type ContentTrendsStatsResponse struct {
	Period        string             `json:"period"`
	ContentCounts []ContentTrendData `json:"content_counts"`
	ViewCounts    []ContentTrendData `json:"view_counts"`
	LikeCounts    []ContentTrendData `json:"like_counts"`
	CommentCounts []ContentTrendData `json:"comment_counts"`
}

// PopularContentResponse 热门内容响应DTO
type PopularContentResponse struct {
	ContentKSUID    string    `json:"content_ksuid"`
	ContentType     string    `json:"content_type"`
	Title           string    `json:"title"`
	UserKSUID       string    `json:"user_ksuid"`
	ViewCount       int64     `json:"view_count"`
	LikeCount       int64     `json:"like_count"`
	CommentCount    int64     `json:"comment_count"`
	FavoriteCount   int64     `json:"favorite_count"`
	PopularityScore float64   `json:"popularity_score"`
	CreatedAt       time.Time `json:"created_at"`
}

// PopularContentListResponse 热门内容列表响应DTO
type PopularContentListResponse struct {
	Contents []*PopularContentResponse `json:"contents"`
	Period   string                    `json:"period"`
	Limit    int                       `json:"limit"`
}

// ActiveUserResponse 活跃用户响应DTO
type ActiveUserResponse struct {
	UserKSUID       string  `json:"user_ksuid"`
	ContentCount    int64   `json:"content_count"`
	TotalViews      int64   `json:"total_views"`
	TotalLikes      int64   `json:"total_likes"`
	ActivityScore   float64 `json:"activity_score"`
	LastActiveAt    time.Time `json:"last_active_at"`
}

// ActiveUserListResponse 活跃用户列表响应DTO
type ActiveUserListResponse struct {
	Users  []*ActiveUserResponse `json:"users"`
	Period string                `json:"period"`
	Limit  int                   `json:"limit"`
}

// DashboardStatsResponse 仪表板统计响应DTO
type DashboardStatsResponse struct {
	Overview        *ContentOverviewResponse         `json:"overview"`
	ContentTypes    []*ContentTypeStatsResponse      `json:"content_types"`
	RecentTrends    *ContentTrendsStatsResponse      `json:"recent_trends"`
	PopularContents *PopularContentListResponse     `json:"popular_contents"`
	ActiveUsers     *ActiveUserListResponse         `json:"active_users"`
	SystemStats     *SystemStatsResponse            `json:"system_stats"`
	LastUpdated     time.Time                       `json:"last_updated"`
}

// ReportRequest 报告请求DTO
type ReportRequest struct {
	ReportType  string     `json:"report_type" binding:"required"` // daily, weekly, monthly
	ContentType string     `json:"content_type,omitempty"`
	UserKSUID   string     `json:"user_ksuid,omitempty"`
	StartDate   *time.Time `json:"start_date,omitempty"`
	EndDate     *time.Time `json:"end_date,omitempty"`
	Format      string     `json:"format,omitempty"` // json, csv, excel
}

// ReportResponse 报告响应DTO
type ReportResponse struct {
	ReportID    string                 `json:"report_id"`
	ReportType  string                 `json:"report_type"`
	Status      string                 `json:"status"`
	Data        interface{}            `json:"data,omitempty"`
	DownloadURL string                 `json:"download_url,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty"`
}

// 转换函数

// FromInteractionStats 从交互统计转换
func FromInteractionStats(stats *types.InteractionStats) *InteractionStatsResponse {
	if stats == nil {
		return nil
	}

	return &InteractionStatsResponse{
		ContentKSUID:  stats.ContentKSUID,
		LikeCount:     stats.LikeCount,
		DislikeCount:  stats.DislikeCount,
		FavoriteCount: stats.FavoriteCount,
	}
}

// FromUserInteractionStats 从用户交互统计转换
func FromUserInteractionStats(stats *types.UserInteractionStats) *UserInteractionStatsResponse {
	if stats == nil {
		return nil
	}

	return &UserInteractionStatsResponse{
		UserKSUID:              stats.UserKSUID,
		TotalLikesGiven:        stats.TotalLikesGiven,
		TotalLikesReceived:     stats.TotalLikesReceived,
		TotalFavoritesGiven:    stats.TotalFavoritesGiven,
		TotalFavoritesReceived: stats.TotalFavoritesReceived,
	}
}

// FromOverallInteractionStats 从总体交互统计转换
func FromOverallInteractionStats(stats *types.OverallInteractionStats) *OverallInteractionStatsResponse {
	if stats == nil {
		return nil
	}

	return &OverallInteractionStatsResponse{
		TotalLikes:     stats.TotalLikes,
		TotalDislikes:  stats.TotalDislikes,
		TotalFavorites: stats.TotalFavorites,
	}
}

// FromContentTypeStats 从内容类型统计转换
func FromContentTypeStats(stats *types.ContentTypeStats) *ContentTypeStatsResponse {
	if stats == nil {
		return nil
	}

	return &ContentTypeStatsResponse{
		ContentType:     stats.ContentType,
		TotalCount:      stats.TotalCount,
		PublishedCount:  stats.PublishedCount,
		DraftCount:      stats.DraftCount,
		ArchivedCount:   stats.ArchivedCount,
		DeletedCount:    stats.DeletedCount,
		TotalViews:      stats.TotalViews,
		TotalLikes:      stats.TotalLikes,
		TotalComments:   stats.TotalComments,
		TotalFavorites:  stats.TotalFavorites,
		AvgViews:        stats.AvgViews,
		AvgLikes:        stats.AvgLikes,
	}
}

// FromContentTrends 从内容趋势转换
func FromContentTrends(trends *types.ContentTrends) *ContentTrendsStatsResponse {
	if trends == nil {
		return nil
	}

	contentCounts := make([]ContentTrendData, len(trends.ContentCounts))
	for i, data := range trends.ContentCounts {
		contentCounts[i] = ContentTrendData{
			Date:  data.Date,
			Value: data.Value,
		}
	}

	viewCounts := make([]ContentTrendData, len(trends.ViewCounts))
	for i, data := range trends.ViewCounts {
		viewCounts[i] = ContentTrendData{
			Date:  data.Date,
			Value: data.Value,
		}
	}

	likeCounts := make([]ContentTrendData, len(trends.LikeCounts))
	for i, data := range trends.LikeCounts {
		likeCounts[i] = ContentTrendData{
			Date:  data.Date,
			Value: data.Value,
		}
	}

	commentCounts := make([]ContentTrendData, len(trends.CommentCounts))
	for i, data := range trends.CommentCounts {
		commentCounts[i] = ContentTrendData{
			Date:  data.Date,
			Value: data.Value,
		}
	}

	return &ContentTrendsStatsResponse{
		Period:        trends.Period,
		ContentCounts: contentCounts,
		ViewCounts:    viewCounts,
		LikeCounts:    likeCounts,
		CommentCounts: commentCounts,
	}
}

// FromCategoryStats 从分类统计转换
func FromCategoryStats(stats *types.CategoryStats) *CategoryStatsResponse {
	if stats == nil {
		return nil
	}

	return &CategoryStatsResponse{
		CategoryID:    stats.CategoryID,
		CategoryName:  stats.CategoryName,
		ContentCount:  stats.ContentCount,
		TotalViews:    stats.TotalViews,
		TotalLikes:    stats.TotalLikes,
		TotalComments: stats.TotalComments,
	}
}

// FromTagStats 从标签统计转换
func FromTagStats(stats *types.TagStats) *TagStatsResponse {
	if stats == nil {
		return nil
	}

	return &TagStatsResponse{
		TagName:         stats.TagName,
		ContentCount:    stats.ContentCount,
		TotalViews:      stats.TotalViews,
		TotalLikes:      stats.TotalLikes,
		PopularityScore: stats.PopularityScore,
	}
}

// FromOperationLogStats 从操作日志统计转换
func FromOperationLogStats(stats *model.OperationLogStats) *OperationLogStatsResponse {
	if stats == nil {
		return nil
	}

	return &OperationLogStatsResponse{
		TotalOperations:      stats.TotalOperations,
		OperationsByType:     stats.OperationsByType,
		OperationsByTarget:   stats.OperationsByTarget,
		OperationsByOperator: stats.OperationsByOperator,
		RecentOperations:     stats.RecentOperations,
	}
}

// FromContentCacheStats 从内容缓存统计转换
func FromContentCacheStats(stats *model.ContentCacheStats) *CacheStatsResponse {
	if stats == nil {
		return nil
	}

	return &CacheStatsResponse{
		TotalCaches:   stats.TotalCaches,
		CachesByType:  stats.CachesByType,
		ExpiredCaches: stats.ExpiredCaches,
		CacheHitRate:  stats.CacheHitRate,
		AvgCacheAge:   stats.AvgCacheAge,
	}
}

// BatchFromInteractionStats 批量从交互统计转换
func BatchFromInteractionStats(statsMap map[string]*types.InteractionStats) map[string]*InteractionStatsResponse {
	if statsMap == nil {
		return make(map[string]*InteractionStatsResponse)
	}

	result := make(map[string]*InteractionStatsResponse)
	for contentKSUID, stats := range statsMap {
		result[contentKSUID] = FromInteractionStats(stats)
	}
	return result
}
