package client

import (
	"fmt"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
	
	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// InteractionServiceClient 交互服务客户端接口
type InteractionServiceClient interface {
	// 点赞统计
	GetContentLikeStats(contentKSUID string) (*types.LikeStats, error)
	BatchGetContentLikeStats(contentKSUIDs []string) (map[string]*types.LikeStats, error)

	// 收藏统计
	GetContentFavoriteStats(contentKSUID string) (*types.FavoriteStats, error)
	BatchGetContentFavoriteStats(contentKSUIDs []string) (map[string]*types.FavoriteStats, error)

	// 综合统计
	GetContentInteractionStats(contentKSUID string) (*types.InteractionStats, error)
	BatchGetContentInteractionStats(contentKSUIDs []string) (map[string]*types.InteractionStats, error)

	// 用户交互统计
	GetUserInteractionStats(userKSUID string) (*types.UserInteractionStats, error)

	// 总体统计
	GetOverallInteractionStats() (*types.OverallInteractionStats, error)
	
	// 健康检查
	HealthCheck() error
}

// interactionServiceClient 交互服务客户端实现
type interactionServiceClient struct {
	baseClient *BaseClient
}

// NewInteractionServiceClient 创建交互服务客户端
func NewInteractionServiceClient(baseURL string, timeout time.Duration) InteractionServiceClient {
	config := ClientConfig{
		BaseURL:    baseURL,
		Timeout:    timeout,
		RetryCount: 2, // 交互服务重试次数较少
		RetryDelay: 500 * time.Millisecond,
	}
	
	return &interactionServiceClient{
		baseClient: NewBaseClient(config),
	}
}

// GetContentLikeStats 获取内容点赞统计
func (c *interactionServiceClient) GetContentLikeStats(contentKSUID string) (*types.LikeStats, error) {
	// 使用实际的interaction-service API端点
	endpoint := fmt.Sprintf("/internal/v1/like/content/counts?content_ksuid=%s", contentKSUID)
	
	var response struct {
		Code int `json:"code"`
		Data struct {
			LikeCount    int64 `json:"like_count"`
			DislikeCount int64 `json:"dislike_count"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	
	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容点赞统计失败")
		return nil, fmt.Errorf("failed to get content like stats: %w", err)
	}
	
	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}
	
	return &types.LikeStats{
		ContentKSUID: contentKSUID,
		LikeCount:    response.Data.LikeCount,
		DislikeCount: response.Data.DislikeCount,
	}, nil
}

// BatchGetContentLikeStats 批量获取内容点赞统计
func (c *interactionServiceClient) BatchGetContentLikeStats(contentKSUIDs []string) (map[string]*types.LikeStats, error) {
	// 使用实际的interaction-service API端点
	endpoint := "/internal/v1/like/content/stats/batch"
	
	requestBody := map[string]interface{}{
		"content_ksuids": contentKSUIDs,
	}
	
	var response struct {
		Code int `json:"code"`
		Data struct {
			Stats map[string]struct {
				ContentKSUID string `json:"content_ksuid"`
				LikeCount    int64  `json:"like_count"`
				DislikeCount int64  `json:"dislike_count"`
			} `json:"stats"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	
	err := c.baseClient.Post(endpoint, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(contentKSUIDs)).
			Msg("批量获取内容点赞统计失败")
		return nil, fmt.Errorf("failed to batch get content like stats: %w", err)
	}
	
	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}
	
	// 转换响应格式
	result := make(map[string]*types.LikeStats)
	for contentKSUID, stats := range response.Data.Stats {
		result[contentKSUID] = &types.LikeStats{
			ContentKSUID: stats.ContentKSUID,
			LikeCount:    stats.LikeCount,
			DislikeCount: stats.DislikeCount,
		}
	}
	
	return result, nil
}

// GetContentFavoriteStats 获取内容收藏统计
func (c *interactionServiceClient) GetContentFavoriteStats(contentKSUID string) (*types.FavoriteStats, error) {
	endpoint := fmt.Sprintf("/internal/v1/favorite/content-stats?content_ksuid=%s", contentKSUID)
	
	var response struct {
		Code int `json:"code"`
		Data struct {
			ContentKSUID  string `json:"content_ksuid"`
			FavoriteCount int64  `json:"favorite_count"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	
	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容收藏统计失败")
		return nil, fmt.Errorf("failed to get content favorite stats: %w", err)
	}
	
	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}
	
	return &types.FavoriteStats{
		ContentKSUID:  response.Data.ContentKSUID,
		FavoriteCount: response.Data.FavoriteCount,
	}, nil
}

// BatchGetContentFavoriteStats 批量获取内容收藏统计
func (c *interactionServiceClient) BatchGetContentFavoriteStats(contentKSUIDs []string) (map[string]*types.FavoriteStats, error) {
	endpoint := "/internal/v1/favorite/content-stats/batch"
	
	requestBody := map[string]interface{}{
		"content_ksuids": contentKSUIDs,
	}
	
	var response struct {
		Code int `json:"code"`
		Data struct {
			Stats map[string]struct {
				ContentKSUID  string `json:"content_ksuid"`
				FavoriteCount int64  `json:"favorite_count"`
			} `json:"stats"`
		} `json:"data"`
		Msg string `json:"msg"`
	}
	
	err := c.baseClient.Post(endpoint, requestBody, &response)
	if err != nil {
		log.Error().
			Err(err).
			Int("count", len(contentKSUIDs)).
			Msg("批量获取内容收藏统计失败")
		return nil, fmt.Errorf("failed to batch get content favorite stats: %w", err)
	}
	
	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}
	
	// 转换响应格式
	result := make(map[string]*types.FavoriteStats)
	for contentKSUID, stats := range response.Data.Stats {
		result[contentKSUID] = &types.FavoriteStats{
			ContentKSUID:  stats.ContentKSUID,
			FavoriteCount: stats.FavoriteCount,
		}
	}
	
	return result, nil
}

// GetContentInteractionStats 获取内容交互统计
func (c *interactionServiceClient) GetContentInteractionStats(contentKSUID string) (*types.InteractionStats, error) {
	// 并发获取点赞和收藏统计
	var wg sync.WaitGroup
	var mu sync.Mutex
	var likeStats *types.LikeStats
	var favoriteStats *types.FavoriteStats
	var errors []error
	
	// 获取点赞统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		stats, err := c.GetContentLikeStats(contentKSUID)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to get like stats: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		likeStats = stats
		mu.Unlock()
	}()
	
	// 获取收藏统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		stats, err := c.GetContentFavoriteStats(contentKSUID)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to get favorite stats: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		favoriteStats = stats
		mu.Unlock()
	}()
	
	wg.Wait()
	
	if len(errors) > 0 {
		log.Error().
			Str("content_ksuid", contentKSUID).
			Interface("errors", errors).
			Msg("获取交互统计部分失败")
		// 不完全失败，继续处理已获取的数据
	}
	
	// 合并统计数据
	result := &types.InteractionStats{
		ContentKSUID:  contentKSUID,
		LikeCount:     0,
		DislikeCount:  0,
		FavoriteCount: 0,
	}
	
	if likeStats != nil {
		result.LikeCount = likeStats.LikeCount
		result.DislikeCount = likeStats.DislikeCount
	}
	
	if favoriteStats != nil {
		result.FavoriteCount = favoriteStats.FavoriteCount
	}
	
	return result, nil
}

// BatchGetContentInteractionStats 批量获取内容交互统计
func (c *interactionServiceClient) BatchGetContentInteractionStats(contentKSUIDs []string) (map[string]*types.InteractionStats, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]*types.InteractionStats), nil
	}

	// 并发获取点赞和收藏统计
	var wg sync.WaitGroup
	var mu sync.Mutex
	var likeStatsMap map[string]*types.LikeStats
	var favoriteStatsMap map[string]*types.FavoriteStats
	var errors []error

	// 批量获取点赞统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		stats, err := c.BatchGetContentLikeStats(contentKSUIDs)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to batch get like stats: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		likeStatsMap = stats
		mu.Unlock()
	}()

	// 批量获取收藏统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		stats, err := c.BatchGetContentFavoriteStats(contentKSUIDs)
		if err != nil {
			mu.Lock()
			errors = append(errors, fmt.Errorf("failed to batch get favorite stats: %w", err))
			mu.Unlock()
			return
		}
		mu.Lock()
		favoriteStatsMap = stats
		mu.Unlock()
	}()

	wg.Wait()

	if len(errors) > 0 {
		log.Error().
			Int("content_count", len(contentKSUIDs)).
			Interface("errors", errors).
			Msg("批量获取交互统计部分失败")
		// 不完全失败，继续处理已获取的数据
	}

	// 合并统计数据
	result := make(map[string]*types.InteractionStats)
	for _, contentKSUID := range contentKSUIDs {
		interactionStats := &types.InteractionStats{
			ContentKSUID:  contentKSUID,
			LikeCount:     0,
			DislikeCount:  0,
			FavoriteCount: 0,
		}

		if likeStatsMap != nil {
			if likeStats, exists := likeStatsMap[contentKSUID]; exists {
				interactionStats.LikeCount = likeStats.LikeCount
				interactionStats.DislikeCount = likeStats.DislikeCount
			}
		}

		if favoriteStatsMap != nil {
			if favoriteStats, exists := favoriteStatsMap[contentKSUID]; exists {
				interactionStats.FavoriteCount = favoriteStats.FavoriteCount
			}
		}

		result[contentKSUID] = interactionStats
	}

	return result, nil
}

// GetUserInteractionStats 获取用户交互统计
func (c *interactionServiceClient) GetUserInteractionStats(userKSUID string) (*types.UserInteractionStats, error) {
	endpoint := fmt.Sprintf("/internal/v1/interaction/user/%s/stats", userKSUID)

	var response struct {
		Code int `json:"code"`
		Data struct {
			UserKSUID           string `json:"user_ksuid"`
			TotalLikesGiven     int64  `json:"total_likes_given"`
			TotalLikesReceived  int64  `json:"total_likes_received"`
			TotalFavoritesGiven int64  `json:"total_favorites_given"`
			TotalFavoritesReceived int64 `json:"total_favorites_received"`
		} `json:"data"`
		Msg string `json:"msg"`
	}

	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取用户交互统计失败")
		return nil, fmt.Errorf("failed to get user interaction stats: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return &types.UserInteractionStats{
		UserKSUID:           response.Data.UserKSUID,
		TotalLikesGiven:     response.Data.TotalLikesGiven,
		TotalLikesReceived:  response.Data.TotalLikesReceived,
		TotalFavoritesGiven: response.Data.TotalFavoritesGiven,
		TotalFavoritesReceived: response.Data.TotalFavoritesReceived,
	}, nil
}

// GetOverallInteractionStats 获取总体交互统计
func (c *interactionServiceClient) GetOverallInteractionStats() (*types.OverallInteractionStats, error) {
	endpoint := "/internal/v1/interaction/stats/overall"

	var response struct {
		Code int `json:"code"`
		Data struct {
			TotalLikes     int64 `json:"total_likes"`
			TotalDislikes  int64 `json:"total_dislikes"`
			TotalFavorites int64 `json:"total_favorites"`
		} `json:"data"`
		Msg string `json:"msg"`
	}

	err := c.baseClient.Get(endpoint, &response)
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取总体交互统计失败")
		return nil, fmt.Errorf("failed to get overall interaction stats: %w", err)
	}

	if response.Code < 0 {
		return nil, types.NewManagementError(types.ErrCodeServiceError, response.Msg)
	}

	return &types.OverallInteractionStats{
		TotalLikes:     response.Data.TotalLikes,
		TotalDislikes:  response.Data.TotalDislikes,
		TotalFavorites: response.Data.TotalFavorites,
	}, nil
}

// HealthCheck 健康检查
func (c *interactionServiceClient) HealthCheck() error {
	return c.baseClient.HealthCheck()
}
