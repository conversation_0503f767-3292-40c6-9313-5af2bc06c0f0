package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
	"pxpat-backend/pkg/cache"
)

// ConfigRepository 配置仓库接口
type ConfigRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, config *model.ManagementConfig) error
	GetByKey(ctx context.Context, configKey string) (*model.ManagementConfig, error)
	Update(ctx context.Context, config *model.ManagementConfig) error
	Delete(ctx context.Context, configKey string) error
	BatchUpdate(ctx context.Context, configs []*model.ManagementConfig) error

	// 查询操作
	GetByCategory(ctx context.Context, category string) ([]*model.ManagementConfig, error)
	GetActiveConfigs(ctx context.Context) ([]*model.ManagementConfig, error)
	GetList(ctx context.Context, query *model.ConfigQuery) (*model.ConfigList, error)

	// 配置操作
	SetConfig(ctx context.Context, key, value, configType, description, category string) error
	GetConfigValue(ctx context.Context, key string) (string, error)
	IsConfigActive(ctx context.Context, key string) (bool, error)
	ToggleConfig(ctx context.Context, key string, isActive bool) error

	// 变更日志
	LogConfigChange(ctx context.Context, changeLog *model.ConfigChangeLog) error
	GetChangeHistory(ctx context.Context, configKey string, limit int) ([]*model.ConfigChangeLog, error)

	// 统计操作
	GetStats(ctx context.Context) (*model.ConfigStats, error)

	// 数据库实例获取（用于事务）
	GetDB() *gorm.DB
}

// configRepository 配置仓库实现
type configRepository struct {
	db           *gorm.DB
	rdb          *redis.Client
	cacheManager cache.Manager
}

// NewConfigRepository 创建配置仓库
func NewConfigRepository(db *gorm.DB, rdb *redis.Client, cacheManager cache.Manager) ConfigRepository {
	return &configRepository{
		db:           db,
		rdb:          rdb,
		cacheManager: cacheManager,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *configRepository) GetDB() *gorm.DB {
	return r.db
}

// Create 创建配置
func (r *configRepository) Create(ctx context.Context, config *model.ManagementConfig) error {
	log.Debug().
		Str("config_key", config.ConfigKey).
		Str("category", config.Category).
		Msg("开始创建配置记录")

	err := r.db.WithContext(ctx).Create(config).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("config_key", config.ConfigKey).
			Msg("创建配置记录失败")
		return fmt.Errorf("failed to create config: %w", err)
	}

	log.Debug().
		Uint("config_id", config.ID).
		Str("config_key", config.ConfigKey).
		Msg("创建配置记录成功")

	// 清除相关缓存
	r.clearConfigCache(ctx, config.ConfigKey, config.Category)

	return nil
}

// GetByKey 根据键获取配置
func (r *configRepository) GetByKey(ctx context.Context, configKey string) (*model.ManagementConfig, error) {
	log.Debug().
		Str("config_key", configKey).
		Msg("开始获取配置记录")

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("config:key:%s", configKey)
	var config model.ManagementConfig
	if err := r.cacheManager.Get(ctx, cacheKey, &config); err == nil {
		log.Debug().
			Str("config_key", configKey).
			Msg("从缓存获取配置记录成功")
		return &config, nil
	}

	// 从数据库获取
	err := r.db.WithContext(ctx).
		Where("config_key = ?", configKey).
		First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Debug().
				Str("config_key", configKey).
				Msg("配置记录不存在")
			return nil, nil
		}
		log.Error().
			Err(err).
			Str("config_key", configKey).
			Msg("获取配置记录失败")
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	// 缓存结果
	r.cacheManager.Set(ctx, cacheKey, &config, 10*time.Minute)

	log.Debug().
		Str("config_key", configKey).
		Msg("获取配置记录成功")

	return &config, nil
}

// Update 更新配置
func (r *configRepository) Update(ctx context.Context, config *model.ManagementConfig) error {
	log.Debug().
		Str("config_key", config.ConfigKey).
		Msg("开始更新配置记录")

	err := r.db.WithContext(ctx).Save(config).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("config_key", config.ConfigKey).
			Msg("更新配置记录失败")
		return fmt.Errorf("failed to update config: %w", err)
	}

	log.Debug().
		Str("config_key", config.ConfigKey).
		Msg("更新配置记录成功")

	// 清除相关缓存
	r.clearConfigCache(ctx, config.ConfigKey, config.Category)

	return nil
}

// Delete 删除配置
func (r *configRepository) Delete(ctx context.Context, configKey string) error {
	log.Debug().
		Str("config_key", configKey).
		Msg("开始删除配置记录")

	// 先获取配置用于清除缓存
	config, err := r.GetByKey(ctx, configKey)
	if err != nil {
		return err
	}
	if config == nil {
		return nil // 配置不存在，认为删除成功
	}

	result := r.db.WithContext(ctx).
		Where("config_key = ?", configKey).
		Delete(&model.ManagementConfig{})
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("config_key", configKey).
			Msg("删除配置记录失败")
		return fmt.Errorf("failed to delete config: %w", result.Error)
	}

	log.Debug().
		Str("config_key", configKey).
		Int64("affected_rows", result.RowsAffected).
		Msg("删除配置记录成功")

	// 清除相关缓存
	r.clearConfigCache(ctx, configKey, config.Category)

	return nil
}

// BatchUpdate 批量更新配置
func (r *configRepository) BatchUpdate(ctx context.Context, configs []*model.ManagementConfig) error {
	if len(configs) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(configs)).
		Msg("开始批量更新配置记录")

	// 使用事务
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, config := range configs {
		if err := tx.Save(config).Error; err != nil {
			tx.Rollback()
			log.Error().
				Err(err).
				Str("config_key", config.ConfigKey).
				Msg("批量更新配置记录失败")
			return fmt.Errorf("failed to batch update config %s: %w", config.ConfigKey, err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		log.Error().
			Err(err).
			Int("count", len(configs)).
			Msg("批量更新配置记录提交失败")
		return fmt.Errorf("failed to commit batch update: %w", err)
	}

	log.Debug().
		Int("count", len(configs)).
		Msg("批量更新配置记录成功")

	// 清除所有配置缓存
	r.clearAllConfigCache(ctx)

	return nil
}

// GetByCategory 根据分类获取配置
func (r *configRepository) GetByCategory(ctx context.Context, category string) ([]*model.ManagementConfig, error) {
	log.Debug().
		Str("category", category).
		Msg("开始获取分类配置")

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("config:category:%s", category)
	var configs []*model.ManagementConfig
	if err := r.cacheManager.Get(ctx, cacheKey, &configs); err == nil {
		log.Debug().
			Str("category", category).
			Int("count", len(configs)).
			Msg("从缓存获取分类配置成功")
		return configs, nil
	}

	// 从数据库获取
	err := r.db.WithContext(ctx).
		Where("category = ?", category).
		Order("config_key ASC").
		Find(&configs).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("category", category).
			Msg("获取分类配置失败")
		return nil, fmt.Errorf("failed to get configs by category: %w", err)
	}

	// 缓存结果
	r.cacheManager.Set(ctx, cacheKey, &configs, 5*time.Minute)

	log.Debug().
		Str("category", category).
		Int("count", len(configs)).
		Msg("获取分类配置成功")

	return configs, nil
}

// GetActiveConfigs 获取活跃配置
func (r *configRepository) GetActiveConfigs(ctx context.Context) ([]*model.ManagementConfig, error) {
	log.Debug().Msg("开始获取活跃配置")

	// 尝试从缓存获取
	cacheKey := "config:active:all"
	var configs []*model.ManagementConfig
	if err := r.cacheManager.Get(ctx, cacheKey, &configs); err == nil {
		log.Debug().
			Int("count", len(configs)).
			Msg("从缓存获取活跃配置成功")
		return configs, nil
	}

	// 从数据库获取
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("category ASC, config_key ASC").
		Find(&configs).Error
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取活跃配置失败")
		return nil, fmt.Errorf("failed to get active configs: %w", err)
	}

	// 缓存结果
	r.cacheManager.Set(ctx, cacheKey, &configs, 5*time.Minute)

	log.Debug().
		Int("count", len(configs)).
		Msg("获取活跃配置成功")

	return configs, nil
}

// GetList 获取配置列表
func (r *configRepository) GetList(ctx context.Context, query *model.ConfigQuery) (*model.ConfigList, error) {
	log.Debug().
		Interface("query", query).
		Msg("开始获取配置列表")

	// 构建查询
	db := r.db.WithContext(ctx).Model(&model.ManagementConfig{})

	// 添加查询条件
	if query.Category != "" {
		db = db.Where("category = ?", query.Category)
	}
	if query.IsActive != nil {
		db = db.Where("is_active = ?", *query.IsActive)
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		log.Error().
			Err(err).
			Msg("获取配置总数失败")
		return nil, fmt.Errorf("failed to count configs: %w", err)
	}

	// 设置分页参数
	page := query.Page
	if page <= 0 {
		page = 1
	}
	limit := query.Limit
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	offset := (page - 1) * limit

	// 查询数据
	var configs []*model.ManagementConfig
	err := db.Order("category ASC, config_key ASC").
		Offset(offset).
		Limit(limit).
		Find(&configs).Error
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取配置列表失败")
		return nil, fmt.Errorf("failed to get config list: %w", err)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	result := &model.ConfigList{
		Configs:    configs,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	log.Debug().
		Int64("total", total).
		Int("page", page).
		Int("limit", limit).
		Msg("获取配置列表成功")

	return result, nil
}

// SetConfig 设置配置
func (r *configRepository) SetConfig(ctx context.Context, key, value, configType, description, category string) error {
	log.Debug().
		Str("config_key", key).
		Str("category", category).
		Msg("开始设置配置")

	// 检查是否已存在
	existingConfig, err := r.GetByKey(ctx, key)
	if err != nil {
		return err
	}

	if existingConfig != nil {
		// 更新现有配置
		existingConfig.ConfigValue = value
		if description != "" {
			existingConfig.Description = description
		}
		return r.Update(ctx, existingConfig)
	}

	// 创建新配置
	newConfig := model.NewManagementConfig(key, value, configType, description, category)
	return r.Create(ctx, newConfig)
}

// GetConfigValue 获取配置值
func (r *configRepository) GetConfigValue(ctx context.Context, key string) (string, error) {
	log.Debug().
		Str("config_key", key).
		Msg("开始获取配置值")

	config, err := r.GetByKey(ctx, key)
	if err != nil {
		return "", err
	}
	if config == nil {
		log.Debug().
			Str("config_key", key).
			Msg("配置不存在")
		return "", nil
	}

	if !config.IsActive {
		log.Debug().
			Str("config_key", key).
			Msg("配置未激活")
		return "", nil
	}

	log.Debug().
		Str("config_key", key).
		Str("config_value", config.ConfigValue).
		Msg("获取配置值成功")

	return config.ConfigValue, nil
}
