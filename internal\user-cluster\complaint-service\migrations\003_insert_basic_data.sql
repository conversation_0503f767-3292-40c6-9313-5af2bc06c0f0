-- 基础数据初始化

-- 1. 插入国家地区数据
INSERT INTO complaint_countries (code, name, name_en, name_local, continent, region, sub_region, flag, phone_code, currency, time_zone, sort_order)
VALUES
    ('CN', '中国', 'China', '中国', 'Asia', 'East Asia', 'Mainland China', '🇨🇳', '86', 'CNY', 'Asia/Shanghai', 1),
    ('HK', '中国香港', 'Hong Kong', '香港', 'Asia', 'East Asia', 'China SAR', '🇭🇰', '852', 'HKD', 'Asia/Hong_Kong', 2),
    ('MO', '中国澳门', 'Macao', '澳門', 'Asia', 'East Asia', 'China SAR', '🇲🇴', '853', 'MOP', 'Asia/Macau', 3),
    ('TW', '中国台湾', 'Taiwan', '台灣', 'Asia', 'East Asia', 'Taiwan', '🇹🇼', '886', 'TWD', 'Asia/Taipei', 4),
    ('US', '美国', 'United States', 'United States', 'North America', 'Northern America', 'United States', '🇺🇸', '1', 'USD', 'America/New_York', 5),
    ('JP', '日本', 'Japan', '日本', 'Asia', 'East Asia', 'Japan', '🇯🇵', '81', 'JPY', 'Asia/Tokyo', 6),
    ('KR', '韩国', 'South Korea', '대한민국', 'Asia', 'East Asia', 'Korea', '🇰🇷', '82', 'KRW', 'Asia/Seoul', 7),
    ('GB', '英国', 'United Kingdom', 'United Kingdom', 'Europe', 'Northern Europe', 'United Kingdom', '🇬🇧', '44', 'GBP', 'Europe/London', 8),
    ('FR', '法国', 'France', 'France', 'Europe', 'Western Europe', 'France', '🇫🇷', '33', 'EUR', 'Europe/Paris', 9),
    ('DE', '德国', 'Germany', 'Deutschland', 'Europe', 'Western Europe', 'Germany', '🇩🇪', '49', 'EUR', 'Europe/Berlin', 10),
    ('CA', '加拿大', 'Canada', 'Canada', 'North America', 'Northern America', 'Canada', '🇨🇦', '1', 'CAD', 'America/Toronto', 11),
    ('AU', '澳大利亚', 'Australia', 'Australia', 'Oceania', 'Australia and New Zealand', 'Australia', '🇦🇺', '61', 'AUD', 'Australia/Sydney', 12),
    ('SG', '新加坡', 'Singapore', 'Singapore', 'Asia', 'South-Eastern Asia', 'Singapore', '🇸🇬', '65', 'SGD', 'Asia/Singapore', 13),
    ('MY', '马来西亚', 'Malaysia', 'Malaysia', 'Asia', 'South-Eastern Asia', 'Malaysia', '🇲🇾', '60', 'MYR', 'Asia/Kuala_Lumpur', 14),
    ('TH', '泰国', 'Thailand', 'ประเทศไทย', 'Asia', 'South-Eastern Asia', 'Thailand', '🇹🇭', '66', 'THB', 'Asia/Bangkok', 15)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    name_en = VALUES(name_en),
    name_local = VALUES(name_local),
    continent = VALUES(continent),
    region = VALUES(region),
    sub_region = VALUES(sub_region),
    flag = VALUES(flag),
    phone_code = VALUES(phone_code),
    currency = VALUES(currency),
    time_zone = VALUES(time_zone),
    sort_order = VALUES(sort_order);

-- 2. 插入商标类型数据
INSERT INTO complaint_trademark_categories (category_number, name, description, sort_order)
VALUES
    (1, '第1类 化学原料', '用于工业、科学、农业、园艺、森林的化学品，未加工人造合成树脂，未加工塑料物质，肥料，灭火用合成物，淬火和金属焊接用制剂，保存食品用化学品，鞣料，工业粘合剂。', 1),
    (2, '第2类 颜料油漆', '颜料，清漆，漆，防锈剂和木材防腐剂，着色剂，媒染剂，未加工的天然树脂，画家、装饰家、印刷商和艺术家用金属箔及金属粉。', 2),
    (3, '第3类 日化用品', '洗衣用漂白剂及其他物料，清洁、擦亮、去渍及研磨用制剂，肥皂，香料，香精油，化妆品，洗发水，牙膏。', 3),
    (4, '第4类 燃料油脂', '工业用油及油脂，润滑剂，吸收、喷洒和粘结灰尘用品，燃料（包括马达用的汽油）和照明材料，照明用蜡烛和灯芯。', 4),
    (5, '第5类 医药', '药品，医用和兽医用制剂，医用卫生制剂，医用或兽医用营养食物和物质，婴儿食品，人用和动物用膳食补充剂，膏药，绷敷材料，填塞牙孔用料，牙科用蜡，消毒剂，消灭有害动物制剂，杀真菌剂，除莠剂。', 5),
    (6, '第6类 金属材料', '普通金属及其合金，金属建筑材料，可移动金属建筑物，铁轨用金属材料，非电气用缆索和金属线，小五金具，金属管，保险箱，不属别类的普通金属制品，矿砂。', 6),
    (7, '第7类 机械设备', '机器和机床，马达和引擎（陆地车辆用的除外），机器传动用联轴节和传动机件（陆地车辆用的除外），非手动农业工具，孵化器，自动售货机。', 7),
    (8, '第8类 手工器械', '手工用具和器械（手工操作的），刀、叉和勺餐具，佩刀，剃刀。', 8),
    (9, '第9类 科学仪器', '科学、航海、测量、摄影、电影、光学、衡具、量具、信号、检验（监督）、救护（营救）和教学用装置及仪器，处理、开关、传送、积累、调节或控制电的装置和仪器，录制、传送、重放声音或影像的装置，磁性数据载体，录音盘，光盘，DVD盘和其他数字存储媒介，投币启动装置的机械结构，收银机，计算机器，数据处理装置，计算机，计算机软件，灭火设备。', 9),
    (10, '第10类 医疗器械', '外科、医疗、牙科和兽医用仪器及器械，假肢，假眼和假牙，矫形用品，缝合用材料。', 10),
    (11, '第11类 灯具空调', '照明、加温、蒸汽、烹饪、冷藏、干燥、通风、供水以及卫生设备装置。', 11),
    (12, '第12类 运输工具', '运载工具，陆、空、海用运载装置。', 12),
    (13, '第13类 军火烟火', '军火，火器，军火及子弹，爆炸物，焰火。', 13),
    (14, '第14类 珠宝钟表', '贵重金属及其合金以及不属别类的贵重金属制品或镀有贵重金属的物品，珠宝首饰，宝石，钟表和计时仪器。', 14),
    (15, '第15类 乐器', '乐器。', 15),
    (16, '第16类 办公用品', '纸和纸板，不属别类的纸和纸板制品，印刷品，装订用品，照片，文具，文具或家庭用粘合剂，美术用品，画笔，打字机和办公用品（家具除外），教育或教学用品（仪器除外），包装用塑料物品（不属别类的），印刷铅字，印版。', 16),
    (17, '第17类 橡胶制品', '不属别类的橡胶、古塔胶、树胶、石棉、云母以及这些原材料的制品，生产用半成品塑料制品，包装、填充和绝缘用材料，非金属软管。', 17),
    (18, '第18类 皮革皮具', '皮革和人造皮革，不属别类的皮革和人造皮革制品，毛皮，箱子和旅行袋，雨伞和阳伞，手杖，鞭和马具。', 18),
    (19, '第19类 建筑材料', '非金属的建筑材料，建筑用非金属刚性管，柏油，沥青，可移动非金属建筑物，非金属碑。', 19),
    (20, '第20类 家具', '家具，镜子，相框，不属别类的木、软木、苇、藤、柳条、角、骨、象牙、鲸骨、贝壳、琥珀、珍珠母、海泡石制品，这些材料的代用品或塑料制品。', 20),
    (21, '第21类 厨房洁具', '家庭或厨房用具及容器，梳子及海绵，刷子（画笔除外），制刷材料，清洁用具，钢丝绒，未加工或半加工玻璃（建筑用玻璃除外），不属别类的玻璃器皿、瓷器和陶器。', 21),
    (22, '第22类 绳网袋篷', '缆，绳，网，遮篷，帐篷，防水遮布，帆，袋（不属别类的），衬垫和填充材料（橡胶或塑料除外），纺织用纤维原料。', 22),
    (23, '第23类 纱线丝', '纺织用纱和线。', 23),
    (24, '第24类 布料床单', '不属别类的布料及纺织品，床单和桌布。', 24),
    (25, '第25类 服装鞋帽', '服装，鞋，帽。', 25),
    (26, '第26类 钮扣拉链', '花边及刺绣，饰带及编带，钮扣，领钩扣，饰针及缝针，假花。', 26),
    (27, '第27类 地毯席垫', '地毯，地席，席类，油毡及其他铺地板用品，非纺织品墙帷。', 27),
    (28, '第28类 健身器材', '娱乐品，玩具，不属别类的体育和运动用品，圣诞树用装饰品。', 28),
    (29, '第29类 食品', '肉，鱼，家禽和野味，肉汁，腌渍、冷冻、干制及煮熟的水果和蔬菜，果冻，果酱，蜜饯，蛋，奶和奶制品，食用油和油脂。', 29),
    (30, '第30类 方便食品', '咖啡，茶，可可和咖啡代用品，米，食用淀粉和西米，面粉和谷类制品，面包，糕点和甜食，食用冰，糖，蜂蜜，糖浆，鲜酵母，发酵粉，食盐，芥末，醋，沙司（调味品），调味用香料，饮用冰。', 30),
    (31, '第31类 饲料种籽', '谷物和不属别类的农业、园艺、林业产品，活动物，新鲜水果和蔬菜，种籽，草木和花卉，动物饲料，麦芽。', 31),
    (32, '第32类 啤酒饮料', '啤酒，矿泉水和汽水以及其他不含酒精的饮料，水果饮料及果汁，糖浆及其他供饮料用的制剂。', 32),
    (33, '第33类 酒', '含酒精的饮料（啤酒除外）。', 33),
    (34, '第34类 烟草烟具', '烟草，烟具，火柴。', 34),
    (35, '第35类 广告销售', '广告，实业经营，实业管理，办公事务。', 35),
    (36, '第36类 金融物管', '保险，金融事务，货币事务，不动产事务。', 36),
    (37, '第37类 建筑修理', '房屋建筑，修理，安装服务。', 37),
    (38, '第38类 通讯服务', '电信。', 38),
    (39, '第39类 运输贮藏', '运输，商品包装和贮藏，旅行安排。', 39),
    (40, '第40类 材料加工', '材料处理。', 40),
    (41, '第41类 教育娱乐', '教育，提供培训，娱乐，文体活动。', 41),
    (42, '第42类 网站服务', '科学技术服务和与之相关的研究与设计服务，工业分析与研究，计算机硬件与软件的设计与开发。', 42),
    (43, '第43类 餐饮住宿', '提供食物和饮料服务，临时住宿。', 43),
    (44, '第44类 医疗园艺', '医疗服务，兽医服务，人或动物的卫生和美容服务，农业、园艺和林业服务。', 44),
    (45, '第45类 社会服务', '法律服务，由他人提供的为满足个人需要的私人和社会服务，为保护财产和人身安全的服务。', 45)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    description = VALUES(description),
    sort_order = VALUES(sort_order);

-- 3. 插入违规类别数据
INSERT INTO complaint_violation_categories (code, name, description, parent_code, sort_order, is_active)
VALUES
    ('illegal', '违法违规', '包含违法违规内容的稿件', NULL, 1, TRUE),
    ('illegal_politics', '政治敏感', '包含政治敏感内容的稿件', 'illegal', 1, TRUE),
    ('illegal_terrorism', '暴恐', '包含恐怖主义、暴力内容的稿件', 'illegal', 2, TRUE),
    ('illegal_porn', '色情低俗', '包含色情、低俗内容的稿件', 'illegal', 3, TRUE),
    ('illegal_gambling', '赌博', '包含赌博内容的稿件', 'illegal', 4, TRUE),
    ('illegal_drug', '毒品', '包含毒品相关内容的稿件', 'illegal', 5, TRUE),
    ('illegal_other', '其他违法', '包含其他违法内容的稿件', 'illegal', 6, TRUE),
    
    ('harmful', '有害信息', '包含有害信息的稿件', NULL, 2, TRUE),
    ('harmful_rumor', '谣言', '包含谣言的稿件', 'harmful', 1, TRUE),
    ('harmful_insult', '侮辱诽谤', '包含侮辱、诽谤内容的稿件', 'harmful', 2, TRUE),
    ('harmful_privacy', '侵犯隐私', '侵犯他人隐私的稿件', 'harmful', 3, TRUE),
    ('harmful_minor', '未成年人不良信息', '对未成年人有不良影响的稿件', 'harmful', 4, TRUE),
    ('harmful_other', '其他有害信息', '包含其他有害信息的稿件', 'harmful', 5, TRUE),
    
    ('copyright', '侵权', '侵犯他人权益的稿件', NULL, 3, TRUE),
    ('copyright_piracy', '盗版', '盗用他人作品的稿件', 'copyright', 1, TRUE),
    ('copyright_trademark', '商标侵权', '侵犯他人商标权的稿件', 'copyright', 2, TRUE),
    ('copyright_portrait', '肖像权侵权', '侵犯他人肖像权的稿件', 'copyright', 3, TRUE),
    ('copyright_other', '其他侵权', '其他侵犯他人权益的稿件', 'copyright', 4, TRUE),
    
    ('spam', '垃圾广告', '包含垃圾广告的稿件', NULL, 4, TRUE),
    ('spam_ad', '商业广告', '包含商业广告的稿件', 'spam', 1, TRUE),
    ('spam_fraud', '诈骗信息', '包含诈骗信息的稿件', 'spam', 2, TRUE),
    ('spam_other', '其他垃圾信息', '包含其他垃圾信息的稿件', 'spam', 3, TRUE),
    
    ('other', '其他', '其他违规内容', NULL, 5, TRUE)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    description = VALUES(description),
    parent_code = VALUES(parent_code),
    sort_order = VALUES(sort_order),
    is_active = VALUES(is_active);
