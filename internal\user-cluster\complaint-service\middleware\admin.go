package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/pkg/response"
)

// AdminRequired 管理员权限中间件
func AdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文获取用户角色
		userRole, exists := c.Get("user_role")
		if !exists {
			log.Warn().Msg("用户角色信息不存在")
			response.Error(c, http.StatusForbidden, "权限不足", nil)
			c.Abort()
			return
		}

		// 检查是否为管理员
		if userRole != "admin" && userRole != "super_admin" {
			log.Warn().
				Str("user_role", userRole.(string)).
				Msg("用户权限不足，需要管理员权限")
			response.Error(c, http.StatusForbidden, "需要管理员权限", nil)
			c.Abort()
			return
		}

		c.Next()
	}
}

// SuperAdminRequired 超级管理员权限中间件
func SuperAdminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文获取用户角色
		userRole, exists := c.Get("user_role")
		if !exists {
			log.Warn().Msg("用户角色信息不存在")
			response.Error(c, http.StatusForbidden, "权限不足", nil)
			c.Abort()
			return
		}

		// 检查是否为超级管理员
		if userRole != "super_admin" {
			log.Warn().
				Str("user_role", userRole.(string)).
				Msg("用户权限不足，需要超级管理员权限")
			response.Error(c, http.StatusForbidden, "需要超级管理员权限", nil)
			c.Abort()
			return
		}

		c.Next()
	}
}
