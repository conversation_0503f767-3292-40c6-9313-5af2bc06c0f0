package admin

import (
	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
	"pxpat-backend/pkg/auth"
)

// RegisterAdminRoutes 注册管理员路由
func RegisterAdminRoutes(
	router *gin.RouterGroup,
	adminHandler *handler.AdminHandler,
	jwtManager *auth.Manager,
) {
	// 管理员路由组，需要管理员权限
	admin := router.Group("/admin")
	admin.Use(jwtManager.AuthMiddleware())
	admin.Use(AdminPermissionMiddleware()) // 管理员权限中间件

	// 投诉管理
	complaints := admin.Group("/complaints")
	{
		complaints.GET("", adminHandler.GetComplaintList)                    // 获取投诉列表
		complaints.POST("/:id/process", adminHandler.ProcessComplaint)      // 处理投诉
	}

	// 身份认证管理
	identity := admin.Group("/identity")
	{
		identity.GET("/verifications", adminHandler.GetIdentityVerificationList)  // 获取身份认证列表
		identity.POST("/:id/review", adminHandler.ReviewIdentityVerification)    // 审核身份认证
	}

	// 权益认证管理
	rights := admin.Group("/rights")
	{
		rights.GET("/verifications", adminHandler.GetRightsVerificationList)  // 获取权益认证列表
		rights.POST("/:id/review", adminHandler.ReviewRightsVerification)     // 审核权益认证
	}
}

// AdminPermissionMiddleware 管理员权限中间件
func AdminPermissionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户角色
		userRole := c.GetString("user_role")
		
		// 检查是否为管理员
		if userRole != "admin" && userRole != "super_admin" {
			c.JSON(403, gin.H{
				"error": "需要管理员权限",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
