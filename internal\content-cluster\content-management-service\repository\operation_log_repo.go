package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
	"pxpat-backend/pkg/cache"
)

// OperationLogRepository 操作日志仓库接口
type OperationLogRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, operationLog *model.OperationLog) error
	GetByID(ctx context.Context, id uint) (*model.OperationLog, error)
	GetList(ctx context.Context, query *model.OperationLogQuery) (*model.OperationLogList, error)
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error

	// 查询操作
	GetByOperator(ctx context.Context, operatorKSUID string, limit int) ([]*model.OperationLog, error)
	GetByTarget(ctx context.Context, targetKSUID string, limit int) ([]*model.OperationLog, error)
	GetByOperationType(ctx context.Context, operationType string, limit int) ([]*model.OperationLog, error)
	GetByTimeRange(ctx context.Context, startTime, endTime time.Time, limit int) ([]*model.OperationLog, error)

	// 统计操作
	GetStats(ctx context.Context) (*model.OperationLogStats, error)
	GetOperatorStats(ctx context.Context, operatorKSUID string) (*model.OperationLogStats, error)
	CountByOperationType(ctx context.Context, operationType string) (int64, error)
	CountByTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error)

	// 清理操作
	CleanupOldLogs(ctx context.Context, retentionDays int) (int64, error)

	// 数据库实例获取（用于事务）
	GetDB() *gorm.DB
}

// operationLogRepository 操作日志仓库实现
type operationLogRepository struct {
	db           *gorm.DB
	rdb          *redis.Client
	cacheManager cache.Manager
}

// NewOperationLogRepository 创建操作日志仓库
func NewOperationLogRepository(db *gorm.DB, rdb *redis.Client, cacheManager cache.Manager) OperationLogRepository {
	return &operationLogRepository{
		db:           db,
		rdb:          rdb,
		cacheManager: cacheManager,
	}
}

// GetDB 获取数据库实例，用于事务处理
func (r *operationLogRepository) GetDB() *gorm.DB {
	return r.db
}

// Create 创建操作日志
func (r *operationLogRepository) Create(ctx context.Context, operationLog *model.OperationLog) error {
	log.Debug().
		Str("operator_ksuid", operationLog.OperatorKSUID).
		Str("operation_type", operationLog.OperationType).
		Str("target_ksuid", operationLog.TargetKSUID).
		Msg("开始创建操作日志记录")

	err := r.db.WithContext(ctx).Create(operationLog).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("operator_ksuid", operationLog.OperatorKSUID).
			Str("operation_type", operationLog.OperationType).
			Msg("创建操作日志记录失败")
		return fmt.Errorf("failed to create operation log: %w", err)
	}

	log.Debug().
		Uint("log_id", operationLog.ID).
		Str("operator_ksuid", operationLog.OperatorKSUID).
		Msg("创建操作日志记录成功")

	// 清除相关缓存
	r.clearRelatedCache(ctx, operationLog)

	return nil
}

// GetByID 根据ID获取操作日志
func (r *operationLogRepository) GetByID(ctx context.Context, id uint) (*model.OperationLog, error) {
	log.Debug().
		Uint("log_id", id).
		Msg("开始获取操作日志记录")

	var operationLog model.OperationLog
	err := r.db.WithContext(ctx).First(&operationLog, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Debug().
				Uint("log_id", id).
				Msg("操作日志记录不存在")
			return nil, nil
		}
		log.Error().
			Err(err).
			Uint("log_id", id).
			Msg("获取操作日志记录失败")
		return nil, fmt.Errorf("failed to get operation log: %w", err)
	}

	log.Debug().
		Uint("log_id", id).
		Msg("获取操作日志记录成功")

	return &operationLog, nil
}

// GetList 获取操作日志列表
func (r *operationLogRepository) GetList(ctx context.Context, query *model.OperationLogQuery) (*model.OperationLogList, error) {
	log.Debug().
		Interface("query", query).
		Msg("开始获取操作日志列表")

	// 构建查询
	db := r.db.WithContext(ctx).Model(&model.OperationLog{})

	// 添加查询条件
	if query.OperatorKSUID != "" {
		db = db.Where("operator_ksuid = ?", query.OperatorKSUID)
	}
	if query.OperationType != "" {
		db = db.Where("operation_type = ?", query.OperationType)
	}
	if query.TargetType != "" {
		db = db.Where("target_type = ?", query.TargetType)
	}
	if query.TargetKSUID != "" {
		db = db.Where("target_ksuid = ?", query.TargetKSUID)
	}
	if query.StartTime != nil {
		db = db.Where("created_at >= ?", *query.StartTime)
	}
	if query.EndTime != nil {
		db = db.Where("created_at <= ?", *query.EndTime)
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		log.Error().
			Err(err).
			Msg("获取操作日志总数失败")
		return nil, fmt.Errorf("failed to count operation logs: %w", err)
	}

	// 设置分页参数
	page := query.Page
	if page <= 0 {
		page = 1
	}
	limit := query.Limit
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	offset := (page - 1) * limit

	// 查询数据
	var logs []*model.OperationLog
	err := db.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs).Error
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取操作日志列表失败")
		return nil, fmt.Errorf("failed to get operation logs: %w", err)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	result := &model.OperationLogList{
		Logs:       logs,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	log.Debug().
		Int64("total", total).
		Int("page", page).
		Int("limit", limit).
		Msg("获取操作日志列表成功")

	return result, nil
}

// Delete 删除操作日志
func (r *operationLogRepository) Delete(ctx context.Context, id uint) error {
	log.Debug().
		Uint("log_id", id).
		Msg("开始删除操作日志记录")

	// 先获取记录用于清除缓存
	operationLog, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}
	if operationLog == nil {
		return nil // 记录不存在，认为删除成功
	}

	err = r.db.WithContext(ctx).Delete(&model.OperationLog{}, id).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint("log_id", id).
			Msg("删除操作日志记录失败")
		return fmt.Errorf("failed to delete operation log: %w", err)
	}

	log.Debug().
		Uint("log_id", id).
		Msg("删除操作日志记录成功")

	// 清除相关缓存
	r.clearRelatedCache(ctx, operationLog)

	return nil
}

// BatchDelete 批量删除操作日志
func (r *operationLogRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	log.Debug().
		Int("count", len(ids)).
		Msg("开始批量删除操作日志记录")

	result := r.db.WithContext(ctx).Delete(&model.OperationLog{}, ids)
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Int("count", len(ids)).
			Msg("批量删除操作日志记录失败")
		return fmt.Errorf("failed to batch delete operation logs: %w", result.Error)
	}

	log.Debug().
		Int64("deleted_count", result.RowsAffected).
		Int("requested_count", len(ids)).
		Msg("批量删除操作日志记录成功")

	// 清除统计缓存
	r.clearStatsCache(ctx)

	return nil
}

// clearRelatedCache 清除相关缓存
func (r *operationLogRepository) clearRelatedCache(ctx context.Context, operationLog *model.OperationLog) {
	// 清除统计缓存
	r.clearStatsCache(ctx)

	// 清除操作者相关缓存
	if operationLog.OperatorKSUID != "" {
		cacheKey := fmt.Sprintf("operation_logs:operator:%s", operationLog.OperatorKSUID)
		r.rdb.Del(ctx, cacheKey)
	}

	// 清除目标相关缓存
	if operationLog.TargetKSUID != "" {
		cacheKey := fmt.Sprintf("operation_logs:target:%s", operationLog.TargetKSUID)
		r.rdb.Del(ctx, cacheKey)
	}
}

// clearStatsCache 清除统计缓存
func (r *operationLogRepository) clearStatsCache(ctx context.Context) {
	cacheKeys := []string{
		"operation_logs:stats:overall",
		"operation_logs:stats:by_type",
		"operation_logs:stats:by_operator",
	}

	for _, key := range cacheKeys {
		r.rdb.Del(ctx, key)
	}
}

// GetByOperator 根据操作者获取操作日志
func (r *operationLogRepository) GetByOperator(ctx context.Context, operatorKSUID string, limit int) ([]*model.OperationLog, error) {
	log.Debug().
		Str("operator_ksuid", operatorKSUID).
		Int("limit", limit).
		Msg("开始获取操作者的操作日志")

	if limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200
	}

	var logs []*model.OperationLog
	err := r.db.WithContext(ctx).
		Where("operator_ksuid = ?", operatorKSUID).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("operator_ksuid", operatorKSUID).
			Msg("获取操作者的操作日志失败")
		return nil, fmt.Errorf("failed to get operation logs by operator: %w", err)
	}

	log.Debug().
		Str("operator_ksuid", operatorKSUID).
		Int("count", len(logs)).
		Msg("获取操作者的操作日志成功")

	return logs, nil
}

// GetByTarget 根据目标获取操作日志
func (r *operationLogRepository) GetByTarget(ctx context.Context, targetKSUID string, limit int) ([]*model.OperationLog, error) {
	log.Debug().
		Str("target_ksuid", targetKSUID).
		Int("limit", limit).
		Msg("开始获取目标的操作日志")

	if limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200
	}

	var logs []*model.OperationLog
	err := r.db.WithContext(ctx).
		Where("target_ksuid = ?", targetKSUID).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("target_ksuid", targetKSUID).
			Msg("获取目标的操作日志失败")
		return nil, fmt.Errorf("failed to get operation logs by target: %w", err)
	}

	log.Debug().
		Str("target_ksuid", targetKSUID).
		Int("count", len(logs)).
		Msg("获取目标的操作日志成功")

	return logs, nil
}

// GetByOperationType 根据操作类型获取操作日志
func (r *operationLogRepository) GetByOperationType(ctx context.Context, operationType string, limit int) ([]*model.OperationLog, error) {
	log.Debug().
		Str("operation_type", operationType).
		Int("limit", limit).
		Msg("开始获取指定类型的操作日志")

	if limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200
	}

	var logs []*model.OperationLog
	err := r.db.WithContext(ctx).
		Where("operation_type = ?", operationType).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("operation_type", operationType).
			Msg("获取指定类型的操作日志失败")
		return nil, fmt.Errorf("failed to get operation logs by type: %w", err)
	}

	log.Debug().
		Str("operation_type", operationType).
		Int("count", len(logs)).
		Msg("获取指定类型的操作日志成功")

	return logs, nil
}

// GetByTimeRange 根据时间范围获取操作日志
func (r *operationLogRepository) GetByTimeRange(ctx context.Context, startTime, endTime time.Time, limit int) ([]*model.OperationLog, error) {
	log.Debug().
		Time("start_time", startTime).
		Time("end_time", endTime).
		Int("limit", limit).
		Msg("开始获取时间范围内的操作日志")

	if limit <= 0 {
		limit = 100
	}
	if limit > 500 {
		limit = 500
	}

	var logs []*model.OperationLog
	err := r.db.WithContext(ctx).
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error
	if err != nil {
		log.Error().
			Err(err).
			Time("start_time", startTime).
			Time("end_time", endTime).
			Msg("获取时间范围内的操作日志失败")
		return nil, fmt.Errorf("failed to get operation logs by time range: %w", err)
	}

	log.Debug().
		Time("start_time", startTime).
		Time("end_time", endTime).
		Int("count", len(logs)).
		Msg("获取时间范围内的操作日志成功")

	return logs, nil
}

// GetStats 获取操作日志统计
func (r *operationLogRepository) GetStats(ctx context.Context) (*model.OperationLogStats, error) {
	log.Debug().Msg("开始获取操作日志统计")

	// 尝试从缓存获取
	cacheKey := "operation_logs:stats:overall"
	var stats model.OperationLogStats
	if err := r.cacheManager.Get(ctx, cacheKey, &stats); err == nil {
		log.Debug().Msg("从缓存获取操作日志统计成功")
		return &stats, nil
	}

	// 获取总操作数
	var totalOperations int64
	if err := r.db.WithContext(ctx).Model(&model.OperationLog{}).Count(&totalOperations).Error; err != nil {
		log.Error().Err(err).Msg("获取总操作数失败")
		return nil, fmt.Errorf("failed to count total operations: %w", err)
	}

	// 获取按类型统计
	var typeStats []struct {
		OperationType string `json:"operation_type"`
		Count         int64  `json:"count"`
	}
	err := r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Select("operation_type, COUNT(*) as count").
		Group("operation_type").
		Find(&typeStats).Error
	if err != nil {
		log.Error().Err(err).Msg("获取按类型统计失败")
		return nil, fmt.Errorf("failed to get stats by type: %w", err)
	}

	operationsByType := make(map[string]int64)
	for _, stat := range typeStats {
		operationsByType[stat.OperationType] = stat.Count
	}

	// 获取按目标类型统计
	var targetStats []struct {
		TargetType string `json:"target_type"`
		Count      int64  `json:"count"`
	}
	err = r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Select("target_type, COUNT(*) as count").
		Group("target_type").
		Find(&targetStats).Error
	if err != nil {
		log.Error().Err(err).Msg("获取按目标类型统计失败")
		return nil, fmt.Errorf("failed to get stats by target type: %w", err)
	}

	operationsByTarget := make(map[string]int64)
	for _, stat := range targetStats {
		operationsByTarget[stat.TargetType] = stat.Count
	}

	// 获取按操作者统计（前10名）
	var operatorStats []struct {
		OperatorKSUID string `json:"operator_ksuid"`
		Count         int64  `json:"count"`
	}
	err = r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Select("operator_ksuid, COUNT(*) as count").
		Group("operator_ksuid").
		Order("count DESC").
		Limit(10).
		Find(&operatorStats).Error
	if err != nil {
		log.Error().Err(err).Msg("获取按操作者统计失败")
		return nil, fmt.Errorf("failed to get stats by operator: %w", err)
	}

	operationsByOperator := make(map[string]int64)
	for _, stat := range operatorStats {
		operationsByOperator[stat.OperatorKSUID] = stat.Count
	}

	// 获取最近24小时操作数
	yesterday := time.Now().Add(-24 * time.Hour)
	var recentOperations int64
	err = r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Where("created_at >= ?", yesterday).
		Count(&recentOperations).Error
	if err != nil {
		log.Error().Err(err).Msg("获取最近操作数失败")
		return nil, fmt.Errorf("failed to count recent operations: %w", err)
	}

	stats = model.OperationLogStats{
		TotalOperations:      totalOperations,
		OperationsByType:     operationsByType,
		OperationsByTarget:   operationsByTarget,
		OperationsByOperator: operationsByOperator,
		RecentOperations:     recentOperations,
	}

	// 缓存结果
	r.cacheManager.Set(ctx, cacheKey, &stats, 5*time.Minute)

	log.Debug().
		Int64("total_operations", totalOperations).
		Int64("recent_operations", recentOperations).
		Msg("获取操作日志统计成功")

	return &stats, nil
}

// GetOperatorStats 获取操作者统计
func (r *operationLogRepository) GetOperatorStats(ctx context.Context, operatorKSUID string) (*model.OperationLogStats, error) {
	log.Debug().
		Str("operator_ksuid", operatorKSUID).
		Msg("开始获取操作者统计")

	// 获取操作者总操作数
	var totalOperations int64
	if err := r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Where("operator_ksuid = ?", operatorKSUID).
		Count(&totalOperations).Error; err != nil {
		log.Error().
			Err(err).
			Str("operator_ksuid", operatorKSUID).
			Msg("获取操作者总操作数失败")
		return nil, fmt.Errorf("failed to count operator operations: %w", err)
	}

	// 获取操作者按类型统计
	var typeStats []struct {
		OperationType string `json:"operation_type"`
		Count         int64  `json:"count"`
	}
	err := r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Select("operation_type, COUNT(*) as count").
		Where("operator_ksuid = ?", operatorKSUID).
		Group("operation_type").
		Find(&typeStats).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("operator_ksuid", operatorKSUID).
			Msg("获取操作者按类型统计失败")
		return nil, fmt.Errorf("failed to get operator stats by type: %w", err)
	}

	operationsByType := make(map[string]int64)
	for _, stat := range typeStats {
		operationsByType[stat.OperationType] = stat.Count
	}

	// 获取最近24小时操作数
	yesterday := time.Now().Add(-24 * time.Hour)
	var recentOperations int64
	err = r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Where("operator_ksuid = ? AND created_at >= ?", operatorKSUID, yesterday).
		Count(&recentOperations).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("operator_ksuid", operatorKSUID).
			Msg("获取操作者最近操作数失败")
		return nil, fmt.Errorf("failed to count operator recent operations: %w", err)
	}

	stats := &model.OperationLogStats{
		TotalOperations:      totalOperations,
		OperationsByType:     operationsByType,
		OperationsByTarget:   make(map[string]int64),
		OperationsByOperator: map[string]int64{operatorKSUID: totalOperations},
		RecentOperations:     recentOperations,
	}

	log.Debug().
		Str("operator_ksuid", operatorKSUID).
		Int64("total_operations", totalOperations).
		Msg("获取操作者统计成功")

	return stats, nil
}

// CountByOperationType 根据操作类型计数
func (r *operationLogRepository) CountByOperationType(ctx context.Context, operationType string) (int64, error) {
	log.Debug().
		Str("operation_type", operationType).
		Msg("开始统计指定类型的操作数量")

	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Where("operation_type = ?", operationType).
		Count(&count).Error
	if err != nil {
		log.Error().
			Err(err).
			Str("operation_type", operationType).
			Msg("统计指定类型的操作数量失败")
		return 0, fmt.Errorf("failed to count operations by type: %w", err)
	}

	log.Debug().
		Str("operation_type", operationType).
		Int64("count", count).
		Msg("统计指定类型的操作数量成功")

	return count, nil
}

// CountByTimeRange 根据时间范围计数
func (r *operationLogRepository) CountByTimeRange(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	log.Debug().
		Time("start_time", startTime).
		Time("end_time", endTime).
		Msg("开始统计时间范围内的操作数量")

	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.OperationLog{}).
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Count(&count).Error
	if err != nil {
		log.Error().
			Err(err).
			Time("start_time", startTime).
			Time("end_time", endTime).
			Msg("统计时间范围内的操作数量失败")
		return 0, fmt.Errorf("failed to count operations by time range: %w", err)
	}

	log.Debug().
		Time("start_time", startTime).
		Time("end_time", endTime).
		Int64("count", count).
		Msg("统计时间范围内的操作数量成功")

	return count, nil
}

// CleanupOldLogs 清理旧日志
func (r *operationLogRepository) CleanupOldLogs(ctx context.Context, retentionDays int) (int64, error) {
	log.Info().
		Int("retention_days", retentionDays).
		Msg("开始清理旧操作日志")

	if retentionDays <= 0 {
		return 0, fmt.Errorf("retention days must be positive")
	}

	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)

	result := r.db.WithContext(ctx).
		Where("created_at < ?", cutoffTime).
		Delete(&model.OperationLog{})
	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Int("retention_days", retentionDays).
			Msg("清理旧操作日志失败")
		return 0, fmt.Errorf("failed to cleanup old logs: %w", result.Error)
	}

	deletedCount := result.RowsAffected

	log.Info().
		Int64("deleted_count", deletedCount).
		Int("retention_days", retentionDays).
		Time("cutoff_time", cutoffTime).
		Msg("清理旧操作日志完成")

	// 清除统计缓存
	r.clearStatsCache(ctx)

	return deletedCount, nil
}
